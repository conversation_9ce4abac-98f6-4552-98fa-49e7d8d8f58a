package models

import (
	"time"

	"gorm.io/gorm"
)

// Role 角色模型
type Role struct {
	ID           uint64         `json:"id" gorm:"primaryKey;type:bigint unsigned;comment:角色ID"`
	Name         string         `json:"name" gorm:"size:128;comment:角色名称"`
	Slug         string         `json:"slug" gorm:"size:128;uniqueIndex;comment:角色标识符"`
	GuardName    string         `json:"guard_name" gorm:"size:128;comment:Guard名称"`
	Remark       string         `json:"remark" gorm:"size:255;comment:备注说明"`
	AllMenuAccess bool          `json:"all_menu_access" gorm:"default:false;comment:是否可访问所有菜单"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	Permissions []Permission `gorm:"many2many:admin_role_permissions;foreignKey:ID;References:ID;" json:"permissions,omitempty"`
	Menus       []Menu       `gorm:"many2many:admin_role_menu;foreignKey:ID;References:ID;" json:"menus,omitempty"`
	Users       []AdminUser  `gorm:"many2many:admin_role_users;foreignKey:ID;References:ID;" json:"users,omitempty"`
}

// TableName 指定表名
func (Role) TableName() string {
	return "admin_roles"
}
