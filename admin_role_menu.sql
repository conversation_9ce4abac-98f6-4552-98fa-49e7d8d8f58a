/*
 Navicat Premium Data Transfer

 Source Server         : 1
 Source Server Type    : MySQL
 Source Server Version : 80034
 Source Host           : localhost:3306
 Source Schema         : go_fiber

 Target Server Type    : MySQL
 Target Server Version : 80034
 File Encoding         : 65001

 Date: 25/06/2025 01:40:29
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for admin_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `admin_role_menu`;
CREATE TABLE `admin_role_menu`  (
  `role_id` bigint(0) NOT NULL,
  `menu_id` bigint(0) NOT NULL,
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  UNIQUE INDEX `admin_role_menu_role_id_menu_id_unique`(`role_id`, `menu_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of admin_role_menu
-- ----------------------------
-- 管理员拥有所有菜单权限
INSERT INTO `admin_role_menu` VALUES (1, 1, NOW(), NOW()); -- 管理员 - 控制面板
INSERT INTO `admin_role_menu` VALUES (1, 2, NOW(), NOW()); -- 管理员 - 项目管理
INSERT INTO `admin_role_menu` VALUES (1, 3, NOW(), NOW()); -- 管理员 - 模块管理
INSERT INTO `admin_role_menu` VALUES (1, 4, NOW(), NOW()); -- 管理员 - 域名管理
INSERT INTO `admin_role_menu` VALUES (1, 5, NOW(), NOW()); -- 管理员 - 邀请码管理
INSERT INTO `admin_role_menu` VALUES (1, 6, NOW(), NOW()); -- 管理员 - 友情链接
INSERT INTO `admin_role_menu` VALUES (1, 7, NOW(), NOW()); -- 管理员 - 菜单管理
INSERT INTO `admin_role_menu` VALUES (1, 8, NOW(), NOW()); -- 管理员 - 系统设置
INSERT INTO `admin_role_menu` VALUES (1, 9, NOW(), NOW()); -- 管理员 - 公告管理
INSERT INTO `admin_role_menu` VALUES (1, 10, NOW(), NOW()); -- 管理员 - 关于系统
INSERT INTO `admin_role_menu` VALUES (1, 11, NOW(), NOW()); -- 管理员 - 项目列表
INSERT INTO `admin_role_menu` VALUES (1, 12, NOW(), NOW()); -- 管理员 - 创建项目
INSERT INTO `admin_role_menu` VALUES (1, 13, NOW(), NOW()); -- 管理员 - 项目内容列表
INSERT INTO `admin_role_menu` VALUES (1, 14, NOW(), NOW()); -- 管理员 - 模块列表
INSERT INTO `admin_role_menu` VALUES (1, 15, NOW(), NOW()); -- 管理员 - 创建模块
INSERT INTO `admin_role_menu` VALUES (1, 16, NOW(), NOW()); -- 管理员 - 域名列表
INSERT INTO `admin_role_menu` VALUES (1, 17, NOW(), NOW()); -- 管理员 - 添加域名
INSERT INTO `admin_role_menu` VALUES (1, 18, NOW(), NOW()); -- 管理员 - 邀请码列表
INSERT INTO `admin_role_menu` VALUES (1, 19, NOW(), NOW()); -- 管理员 - 创建邀请码
INSERT INTO `admin_role_menu` VALUES (1, 20, NOW(), NOW()); -- 管理员 - 链接列表
INSERT INTO `admin_role_menu` VALUES (1, 21, NOW(), NOW()); -- 管理员 - 添加链接
INSERT INTO `admin_role_menu` VALUES (1, 22, NOW(), NOW()); -- 管理员 - 菜单列表
INSERT INTO `admin_role_menu` VALUES (1, 23, NOW(), NOW()); -- 管理员 - 创建菜单
INSERT INTO `admin_role_menu` VALUES (1, 24, NOW(), NOW()); -- 管理员 - 基本设置
INSERT INTO `admin_role_menu` VALUES (1, 25, NOW(), NOW()); -- 管理员 - 用户管理
INSERT INTO `admin_role_menu` VALUES (1, 26, NOW(), NOW()); -- 管理员 - 角色管理
INSERT INTO `admin_role_menu` VALUES (1, 27, NOW(), NOW()); -- 管理员 - 公告列表
INSERT INTO `admin_role_menu` VALUES (1, 28, NOW(), NOW()); -- 管理员 - 发布公告
INSERT INTO `admin_role_menu` VALUES (1, 50, NOW(), NOW()); -- 管理员 - 权限管理

-- 普通用户菜单权限
INSERT INTO `admin_role_menu` VALUES (2, 1, NOW(), NOW()); -- 普通用户 - 控制面板
INSERT INTO `admin_role_menu` VALUES (2, 2, NOW(), NOW()); -- 普通用户 - 项目管理
INSERT INTO `admin_role_menu` VALUES (2, 10, NOW(), NOW()); -- 普通用户 - 关于系统
INSERT INTO `admin_role_menu` VALUES (2, 11, NOW(), NOW()); -- 普通用户 - 项目列表
INSERT INTO `admin_role_menu` VALUES (2, 12, NOW(), NOW()); -- 普通用户 - 创建项目
INSERT INTO `admin_role_menu` VALUES (2, 13, NOW(), NOW()); -- 普通用户 - 项目内容列表

SET FOREIGN_KEY_CHECKS = 1;
