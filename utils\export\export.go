package export

import (
    "github.com/xuri/excelize/v2"
    "io"
    "reflect"
    "time"
    "fmt"
)


// ExportStructsToExcel 导出任意 struct 切片到 Excel
func ExportStructsToExcel(slice interface{}, w io.Writer, prefix string) (string, error) {
    v := reflect.ValueOf(slice)
    if v.Kind() != reflect.Slice {
        return "", fmt.Errorf("参数必须为切片")
    }
    if v.Len() == 0 {
        return "", fmt.<PERSON><PERSON><PERSON>("无数据可导出")
    }
    elemType := v.Index(0).Type()
    var headers []string
    var fields []int
    for i := 0; i < elemType.NumField(); i++ {
        tag := elemType.Field(i).Tag.Get("export")
        if tag != "" {
            headers = append(headers, tag)
            fields = append(fields, i)
        }
    }
    f := excelize.NewFile()
    sheet := "Sheet1"
    f.SetSheetName("Sheet1", sheet)
    // 写表头
    for i, h := range headers {
        col, _ := excelize.ColumnNumberToName(i + 1)
        f.SetCellValue(sheet, col+"1", h)
    }
    // 写数据
    for r := 0; r < v.Len(); r++ {
        rowVal := v.Index(r)
        for c, fi := range fields {
            col, _ := excelize.ColumnNumberToName(c + 1)
            f.SetCellValue(sheet, col+fmt.Sprintf("%d", r+2), rowVal.Field(fi).Interface())
        }
    }
    filename := prefix + "_" + time.Now().Format("20060102_150405") + ".xlsx"
    err := f.Write(w)
    return filename, err
}