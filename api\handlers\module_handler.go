package handlers

import (
	"go-fiber-api/api/dto"
	"go-fiber-api/database"
	"go-fiber-api/models"
	"go-fiber-api/utils"
	"encoding/json"
	"strings"
	"strconv"
	"log"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

// GetModules 获取模块列表
// @Summary 获取模块列表
// @Description 获取模块列表，支持分页
// @Tags 模块管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码，默认为1"
// @Param page_size query int false "每页条数，默认为10"
// @Param title query string false "模块名称过滤(模糊匹配)"
// @Param level query int false "安全级别过滤"
// @Param state query int false "状态过滤(1:启用, 0:禁用)"
// @Param is_share query int false "共享状态过滤"
// @Param with_deleted query bool false "是否包含已删除记录"
// @Success 200 {object} dto.StandardResponse{data=dto.PaginatedResponse{items=[]dto.ModuleListItem}}
// @Router /admin/module/index [get]
func GetModules(c *fiber.Ctx) error {
	// 获取分页和过滤参数
	page, _ := strconv.Atoi(c.Query("page", "1"))
	pageSize, _ := strconv.Atoi(c.Query("page_size", "10"))
	title := c.Query("title", "")
	levelStr := c.Query("level", "")
	stateStr := c.Query("state", "")
	isShareStr := c.Query("is_share", "")
	withDeleted := c.Query("with_deleted", "false") == "true"
	
	log.Printf("GetModules - 参数: page=%d, page_size=%d, title=%s, level=%s, state=%s, is_share=%s, with_deleted=%t", 
		page, pageSize, title, levelStr, stateStr, isShareStr, withDeleted)
		
	// 检查用户是否为管理员
	isAdmin := utils.IsAdmin(c)
	log.Printf("GetModules - 用户是否为管理员: %t", isAdmin)
	
	// 构建查询
	var query *gorm.DB
	
	// 管理员可以查看已删除记录
	if withDeleted && isAdmin {
		log.Println("GetModules - 管理员查看包含已删除记录")
		query = database.DB.Unscoped() // 包括已删除的记录
	} else {
		log.Println("GetModules - 只查看未删除记录")
		query = database.DB
	}
	
	query = query.Model(&models.Module{}) // 明确指定模型
	
	// 应用筛选条件
	if title != "" {
		query = query.Where("title LIKE ?", "%"+title+"%")
	}
	
	if levelStr != "" {
		levelInt, err := strconv.Atoi(levelStr)
		if err == nil {
			query = query.Where("level = ?", levelInt)
		}
	}
	
	if stateStr != "" {
		stateInt, err := strconv.Atoi(stateStr)
		if err == nil {
			query = query.Where("state = ?", stateInt)
		}
	}
	
	if isShareStr != "" {
		isShareInt, err := strconv.Atoi(isShareStr)
		if err == nil {
			query = query.Where("is_share = ?", isShareInt)
		}
	}
	
	// 获取当前用户ID
	userID := utils.GetUserIDFromContext(c)
	
	// 根据用户角色过滤
	// 普通用户只能查看自己的模块或共享的模块，管理员可以查看所有模块
	if !isAdmin {
		query = query.Where("(user_id = ? OR is_share = ?)", userID, 20) // 20表示公开
	}
	
	// 如果不是查看已删除记录，则只返回未删除的记录
	if !withDeleted || !isAdmin {
		query = query.Where("deleted_at IS NULL")
	}
	
	// 计算总数
	var total int64
	query.Count(&total)
	log.Printf("GetModules - 查询到总记录数: %d", total)
	
	// 获取分页数据
	var modules []models.Module
	query.Order("id DESC").
		Offset((page - 1) * pageSize).
		Limit(pageSize).
		Find(&modules)
	
	log.Printf("GetModules - 获取到记录数: %d", len(modules))
	
	// 转换为DTO
	var moduleDTOs []dto.ModuleListItem
	for _, module := range modules {
		deleted := false
		if !module.DeletedAt.Time.IsZero() {
			deleted = true
		}
		
		moduleDTOs = append(moduleDTOs, dto.ModuleListItem{
			ID:          module.ID,
			Title:       module.Title,
			Description: module.Description,
			Level:       module.Level,
			IsShare:     module.IsShare,
			State:       module.State,
			Keys:        module.Keys,
			UserID:      module.UserID,
			Deleted:     deleted,
			CreatedAt:   module.CreatedAt.Format("2006-01-02 15:04:05"),
		})
	}
	
	// 返回分页响应
	return utils.SuccessPaginated(c, "获取模块列表成功", moduleDTOs, total, page, pageSize)
}

// GetModule 获取单个模块详情
// @Summary 获取模块详情
// @Description 根据ID获取模块详情
// @Tags 模块管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "模块ID"
// @Success 200 {object} dto.StandardResponse{data=dto.ModuleResponse}
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 404 {object} dto.StandardResponse "模块不存在"
// @Router /admin/module/index/{id} [get]
func GetModule(c *fiber.Ctx) error {
	// 获取模块ID
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的模块ID", err)
	}
	
	// 查询模块
	var module models.Module
	if err := database.DB.First(&module, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "模块不存在")
		}
		return utils.ServerError(c, "获取模块失败", err)
	}
	
	// 获取当前用户ID
	userID := utils.GetUserIDFromContext(c)
	
	// 权限检查：非管理员只能查看自己的模块或共享的模块
	if !utils.IsAdmin(c) && module.UserID != userID && module.IsShare != 20 {
			return utils.Forbidden(c, "您没有权限查看此模块", nil)
	}

	// 处理模块代码中的 {set.参数名} 替换
	// 获取模块代码
	moduleCode := module.Code
	
	// 处理设置键
	if module.Setkeys != "" {
		// 尝试解析为对象格式 {"key": "value"}
		var setkeyMap map[string]interface{}
		if err := json.Unmarshal([]byte(module.Setkeys), &setkeyMap); err == nil {
			// 对象格式：直接遍历键值对
			for keyName, keyValue := range setkeyMap {
				if valueStr, ok := keyValue.(string); ok {
					// 替换模板中的变量
					moduleCode = strings.Replace(moduleCode, "{Set."+keyName+"}", valueStr, -1)
					moduleCode = strings.Replace(moduleCode, "{set."+keyName+"}", valueStr, -1)
				}
			}
		}
	}
	
	// 转换为DTO
	moduleDTO := dto.ModuleResponse{
		ID:          	module.ID,
		Title:       	module.Title,
		Description: 	module.Description,
		Keys:        	module.Keys,
		Setkeys:     	module.Setkeys,
		Code:        	moduleCode,
		OriginalCode: 	module.Code,
		Level:       	module.Level,
		UserID:      	module.UserID,
		IsShare:     	module.IsShare,
		State:       	module.State,
		CreatedAt:   	module.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:   	module.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
	
	return utils.Success(c, "获取模块成功", moduleDTO)
}

// CreateModule 创建模块
// @Summary 创建模块
// @Description 创建新的模块
// @Tags 模块管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param module body dto.ModuleRequest true "模块信息"
// @Success 200 {object} dto.StandardResponse{data=dto.ModuleResponse}
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Router /admin/module/index [post]
func CreateModule(c *fiber.Ctx) error {
	// 解析请求体
	var req dto.ModuleRequest
	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}
	
	// 验证请求数据
	if err := utils.ValidateStruct(req); err != nil {
		return utils.BadRequest(c, "数据验证失败", err)
	}
	
	// 获取当前用户ID
	userID := utils.GetUserIDFromContext(c)
	
	// 创建新模块
	module := models.Module{
		Title:       req.Title,
		Description: req.Description,
		Keys:        req.Keys,
		Setkeys:     req.Setkeys,
		Code:        req.Code,
		Level:       req.Level,
		UserID:      userID,
		IsShare:     req.IsShare,
		State:       req.State,
	}
	
	if err := database.DB.Create(&module).Error; err != nil {
		return utils.ServerError(c, "创建模块失败", err)
	}
	
	// 转换为DTO
	moduleDTO := dto.ModuleResponse{
		ID:          module.ID,
		Title:       module.Title,
		Description: module.Description,
		Keys:        module.Keys,
		Setkeys:     module.Setkeys,
		Code:        module.Code,
		Level:       module.Level,
		UserID:      module.UserID,
		IsShare:     module.IsShare,
		State:       module.State,
		CreatedAt:   module.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:   module.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
	
	return utils.Success(c, "创建模块成功", moduleDTO)
}

// UpdateModule 更新模块
// @Summary 更新模块
// @Description 更新现有模块
// @Tags 模块管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "模块ID"
// @Param module body dto.ModuleUpdateRequest true "模块更新信息"
// @Success 200 {object} dto.StandardResponse{data=dto.ModuleResponse}
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 404 {object} dto.StandardResponse "模块不存在"
// @Router /admin/module/index/{id} [put]
func UpdateModule(c *fiber.Ctx) error {
	// 获取模块ID
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的模块ID", err)
	}
	
	// 查询模块
	var module models.Module
	if err := database.DB.First(&module, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "模块不存在")
		}
		return utils.ServerError(c, "获取模块失败", err)
	}
	
	// 获取当前用户ID
	userID := utils.GetUserIDFromContext(c)
	
	// 权限检查：非管理员只能更新自己的模块
	if !utils.IsAdmin(c) && module.UserID != userID {
			return utils.Forbidden(c, "您没有权限更新此模块", nil)
	}
	
	// 解析请求体
	var req dto.ModuleUpdateRequest
	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}
	
	// 验证请求数据
	if err := utils.ValidateStruct(req); err != nil {
		return utils.BadRequest(c, "数据验证失败", err)
	}
	
	// 准备更新数据
	updates := make(map[string]interface{})
	
	if req.Title != nil {
		updates["title"] = *req.Title
	}
	
	if req.Description != nil {
		updates["description"] = *req.Description
	}
	
	if req.Keys != nil {
		updates["keys"] = *req.Keys
	}
	
	if req.Setkeys != nil {
		updates["setkeys"] = *req.Setkeys
	}
	
	if req.Code != nil {
		updates["code"] = *req.Code
	}
	
	if req.Level != nil {
		updates["level"] = *req.Level
	}
	
	if req.IsShare != nil {
		updates["is_share"] = *req.IsShare
	}
	
	if req.State != nil {
		updates["state"] = *req.State
	}
	
	// 执行更新
	if err := database.DB.Model(&module).Updates(updates).Error; err != nil {
		return utils.ServerError(c, "更新模块失败", err)
	}
	
	// 重新获取更新后的模块
	if err := database.DB.First(&module, id).Error; err != nil {
		return utils.ServerError(c, "获取更新后的模块失败", err)
	}
	
	// 转换为DTO
	moduleDTO := dto.ModuleResponse{
		ID:          module.ID,
		Title:       module.Title,
		Description: module.Description,
		Keys:        module.Keys,
		Setkeys:     module.Setkeys,
		Code:        module.Code,
		Level:       module.Level,
		UserID:      module.UserID,
		IsShare:     module.IsShare,
		State:       module.State,
		CreatedAt:   module.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:   module.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
	
	return utils.Success(c, "更新模块成功", moduleDTO)
}

// DeleteModule 删除模块
// @Summary 删除模块
// @Description 删除现有模块
// @Tags 模块管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "模块ID"
// @Success 200 {object} dto.StandardResponse "删除成功"
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 404 {object} dto.StandardResponse "模块不存在"
// @Router /admin/module/index/{id} [delete]
func DeleteModule(c *fiber.Ctx) error {
	// 获取模块ID
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的模块ID", err)
	}
	
	// 查询模块
	var module models.Module
	if err := database.DB.First(&module, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "模块不存在")
		}
		return utils.ServerError(c, "获取模块失败", err)
	}
	
	// 获取当前用户ID
	userID := utils.GetUserIDFromContext(c)
	
	// 权限检查：非管理员只能删除自己的模块
	if !utils.IsAdmin(c) && module.UserID != userID {
			return utils.Forbidden(c, "您没有权限删除此模块", nil)
	}
	
	// 检查模块是否被项目使用
	var count int64
	if err := database.DB.Model(&models.Project{}).Where("module_id LIKE ?", "%"+strconv.FormatUint(id, 10)+"%").Count(&count).Error; err != nil {
		return utils.ServerError(c, "检查模块使用情况失败", err)
	}
	
	if count > 0 {
		return utils.BadRequest(c, "此模块已被项目使用，无法删除", nil)
	}
	
	// 软删除模块
	if err := database.DB.Delete(&module).Error; err != nil {
		return utils.ServerError(c, "删除模块失败", err)
	}
	
	return utils.Success(c, "删除模块成功", nil)
}

// RestoreModule 恢复已删除的模块
// @Summary 恢复已删除的模块
// @Description 根据ID恢复被软删除的模块
// @Tags 模块管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "模块ID"
// @Success 200 {object} dto.StandardResponse
// @Router /admin/module/restore/{id} [put]
func RestoreModule(c *fiber.Ctx) error {
	db := database.DB.Model(&models.Module{})
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的模块ID", err)
	}
	
	// 执行恢复操作
	// Unscoped用于查询包括已删除记录
	var module models.Module
	result := db.Unscoped().Where("id = ?", id).First(&module)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "模块不存在")
		}
		return utils.ServerError(c, "获取模块失败", result.Error)
	}
	
	// 检查记录是否已被删除
	if module.DeletedAt.Time.IsZero() {
		return utils.BadRequest(c, "此模块未被删除", nil)
	}

	// 检查权限：普通用户只能恢复自己的模块
	if !utils.IsAdmin(c) {
		userID := utils.GetUserIDFromContext(c)
		if module.UserID != userID {
			return utils.Forbidden(c, "您没有权限恢复此模块", nil)
		}
	}
	
	// 执行恢复
	if err := db.Unscoped().Model(&models.Module{}).Where("id = ?", id).Update("deleted_at", nil).Error; err != nil {
		return utils.ServerError(c, "恢复模块失败", err)
	}
	
	return utils.Success(c, "恢复模块成功", nil)
} 