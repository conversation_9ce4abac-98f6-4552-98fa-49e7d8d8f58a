package routes

import (
	"go-fiber-api/api/handlers"
	"go-fiber-api/api/middleware"
	"go-fiber-api/utils"
	"github.com/gofiber/fiber/v2"
)

// SetupArticleRoutes 设置公告相关路由
func SetupArticleRoutes(router fiber.Router) {
	article := router.Group("/article")

	// 应用认证中间件
	article.Use(utils.RequireAuthentication)

	// 公告管理路由
	article.Get("/index", middleware.RequirePermission("article.view"), handlers.GetArticles)
	article.Post("/index", middleware.RequirePermission("article.create"), handlers.CreateArticle)
	article.Get("/index/:id", middleware.RequirePermission("article.view"), handlers.GetArticle)
	article.Put("/index/:id", middleware.RequirePermission("article.edit"), handlers.UpdateArticle)
	article.Delete("/index/:id", middleware.RequirePermission("article.delete"), handlers.DeleteArticle)

	// 公告发布路由
	article.Post("/publish/:id", middleware.RequirePermission("article.publish"), handlers.PublishArticle)
}
