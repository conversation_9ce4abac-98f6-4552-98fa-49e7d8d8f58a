package utils

import (
	"encoding/json"
	"fmt"
	"go-fiber-api/database"
	"go-fiber-api/models"
	"log"
	"sync"
	"time"
)

const (
	projectContentCountCachePrefix = "project_content_count:"
	projectContentCountExpiration  = 15 * time.Minute // 缓存15分钟
)

var (
	projectCacheMutex sync.RWMutex
)

// ProjectCacheService 项目缓存服务
type ProjectCacheService struct{}

// GetProjectContentCountsBatch 批量获取项目内容统计，使用缓存
func (s *ProjectCacheService) GetProjectContentCountsBatch(projectIDs []uint64) (map[uint64]int64, error) {
	if len(projectIDs) == 0 {
		return make(map[uint64]int64), nil
	}

	projectCacheMutex.RLock()
	defer projectCacheMutex.RUnlock()

	result := make(map[uint64]int64)
	var uncachedIDs []uint64

	// 检查缓存
	for _, projectID := range projectIDs {
		cacheKey := fmt.Sprintf("%s%d", projectContentCountCachePrefix, projectID)
		cachedData, err := database.Rdb.Get(database.RedisCtx, cacheKey).Result()
		if err == nil {
			var count int64
			if err := json.Unmarshal([]byte(cachedData), &count); err == nil {
				result[projectID] = count
				continue
			}
		}
		uncachedIDs = append(uncachedIDs, projectID)
	}

	// 如果所有数据都在缓存中，直接返回
	if len(uncachedIDs) == 0 {
		log.Printf("项目内容统计缓存全部命中: %d 个项目", len(projectIDs))
		return result, nil
	}

	log.Printf("项目内容统计缓存部分命中: %d/%d 个项目需要查询数据库", len(uncachedIDs), len(projectIDs))

	// 从数据库批量查询未缓存的数据
	type ContentCountResult struct {
		ProjectID uint64 `json:"project_id"`
		Count     int64  `json:"count"`
	}
	var contentCounts []ContentCountResult
	if err := database.DB.Model(&models.ProjectContent{}).
		Select("project_id, COUNT(*) as count").
		Where("project_id IN ?", uncachedIDs).
		Group("project_id").
		Scan(&contentCounts).Error; err != nil {
		return nil, fmt.Errorf("批量查询项目内容统计失败: %w", err)
	}

	// 将查询结果添加到结果中并缓存
	for _, cc := range contentCounts {
		result[cc.ProjectID] = cc.Count
		
		// 缓存到Redis
		cacheKey := fmt.Sprintf("%s%d", projectContentCountCachePrefix, cc.ProjectID)
		if data, err := json.Marshal(cc.Count); err == nil {
			database.Rdb.Set(database.RedisCtx, cacheKey, data, projectContentCountExpiration)
		}
	}

	// 对于没有内容的项目，设置为0并缓存
	for _, projectID := range uncachedIDs {
		if _, exists := result[projectID]; !exists {
			result[projectID] = 0
			
			// 缓存0值
			cacheKey := fmt.Sprintf("%s%d", projectContentCountCachePrefix, projectID)
			if data, err := json.Marshal(int64(0)); err == nil {
				database.Rdb.Set(database.RedisCtx, cacheKey, data, projectContentCountExpiration)
			}
		}
	}

	log.Printf("项目内容统计查询完成: 缓存命中 %d 个，数据库查询 %d 个", len(projectIDs)-len(uncachedIDs), len(uncachedIDs))
	return result, nil
}

// InvalidateProjectContentCount 清除单个项目的内容统计缓存
func (s *ProjectCacheService) InvalidateProjectContentCount(projectID uint64) error {
	cacheKey := fmt.Sprintf("%s%d", projectContentCountCachePrefix, projectID)
	err := database.Rdb.Del(database.RedisCtx, cacheKey).Err()
	if err != nil {
		return fmt.Errorf("清除项目内容统计缓存失败: %w", err)
	}
	log.Printf("已清除项目 %d 的内容统计缓存", projectID)
	return nil
}

// InvalidateProjectContentCountBatch 批量清除项目内容统计缓存
func (s *ProjectCacheService) InvalidateProjectContentCountBatch(projectIDs []uint64) error {
	if len(projectIDs) == 0 {
		return nil
	}

	var keys []string
	for _, projectID := range projectIDs {
		keys = append(keys, fmt.Sprintf("%s%d", projectContentCountCachePrefix, projectID))
	}

	err := database.Rdb.Del(database.RedisCtx, keys...).Err()
	if err != nil {
		return fmt.Errorf("批量清除项目内容统计缓存失败: %w", err)
	}
	
	log.Printf("已批量清除 %d 个项目的内容统计缓存", len(projectIDs))
	return nil
}

// GetProjectCacheService 获取项目缓存服务实例
func GetProjectCacheService() *ProjectCacheService {
	return &ProjectCacheService{}
}

// WarmupProjectContentCountCache 预热项目内容统计缓存
func (s *ProjectCacheService) WarmupProjectContentCountCache(limit int) error {
	log.Printf("开始预热项目内容统计缓存，限制 %d 个项目", limit)
	
	// 获取最近的项目ID
	var projectIDs []uint64
	if err := database.DB.Model(&models.Project{}).
		Select("id").
		Order("updated_at DESC").
		Limit(limit).
		Pluck("id", &projectIDs).Error; err != nil {
		return fmt.Errorf("获取项目ID列表失败: %w", err)
	}

	if len(projectIDs) == 0 {
		log.Println("没有项目需要预热缓存")
		return nil
	}

	// 批量获取统计数据（这会自动缓存）
	_, err := s.GetProjectContentCountsBatch(projectIDs)
	if err != nil {
		return fmt.Errorf("预热项目内容统计缓存失败: %w", err)
	}

	log.Printf("项目内容统计缓存预热完成: %d 个项目", len(projectIDs))
	return nil
}

// GetCacheStats 获取缓存统计信息
func (s *ProjectCacheService) GetCacheStats() (map[string]interface{}, error) {
	// 获取Redis中项目内容统计缓存的键数量
	keys, err := database.Rdb.Keys(database.RedisCtx, projectContentCountCachePrefix+"*").Result()
	if err != nil {
		return nil, fmt.Errorf("获取缓存键失败: %w", err)
	}

	stats := map[string]interface{}{
		"project_content_count_cache_keys": len(keys),
		"cache_prefix":                     projectContentCountCachePrefix,
		"cache_expiration_minutes":         int(projectContentCountExpiration.Minutes()),
	}

	return stats, nil
}
