package models

import (
	"time"

	"gorm.io/gorm"
)

// Domain 域名模型
type Domain struct {
	ID        uint64           `json:"id" gorm:"primaryKey;type:int unsigned;comment:域名ID"`
	Domain    string         `json:"domain" gorm:"not null;index:domain_domain_index;size:255;default:'128';comment:域名"`
	Type      int8           `json:"type" gorm:"not null;type:tinyint;default:10;comment:类型 10 过滤域名/20 切换使用的域名"`
	State     int            `json:"state" gorm:"not null;default:10;comment:状态"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName 指定表名
func (Domain) TableName() string {
	return "domain"
} 