package models

import (
	"time"
	"gorm.io/gorm"
)

// Menu 菜单模型
type Menu struct {
	ID        uint64    `json:"id" gorm:"primaryKey;autoIncrement;type:bigint unsigned;comment:菜单ID"`
	ParentID  uint64    `json:"parent_id" gorm:"type:bigint;not null;default:0;comment:父级菜单ID"`
	Order     uint64    `json:"order" gorm:"type:bigint;not null;default:0;comment:排序"`
	Title     string    `json:"title" gorm:"size:50;not null;comment:菜单标题"`
	Icon      string    `json:"icon" gorm:"size:50;comment:菜单图标"`
	URI       string    `json:"uri" gorm:"size:50;comment:菜单URI"`
	CreatedAt time.Time `json:"created_at" gorm:"comment:创建时间"`
	UpdatedAt time.Time `json:"updated_at" gorm:"comment:更新时间"`

	// 关联关系
	Roles []Role `gorm:"many2many:admin_role_menu;foreignKey:ID;joinForeignKey:MenuID;References:ID;joinReferences:RoleID" json:"roles,omitempty"`
	Children []Menu `gorm:"-" json:"children,omitempty"` // 自引用，用于构建菜单树
}

// TableName 指定表名
func (Menu) TableName() string {
	return "admin_menu"
}

// AfterFind 在查询后加载子权限
func (m *Menu) AfterFind(tx *gorm.DB) error {
    if m.ID > 0 {
        var children []Menu
        tx.Where("parent_id = ?", m.ID).Find(&children)
        m.Children = children
    }
    return nil
}