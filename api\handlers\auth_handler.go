package handlers

import (
	// Need context for Redis operations - No longer needed here di
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"log"
	"math/big"
	"os"
	"path/filepath"
	"strings"
	"time"

	"go-fiber-api/api/dto"
	"go-fiber-api/config"
	"go-fiber-api/database"
	"go-fiber-api/models"
	"go-fiber-api/utils"
	"go-fiber-api/utils/email"
	"go-fiber-api/utils/queue"

	// "github.com/go-redis/redis/v8" // Removed, now handled in utils
	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
)

const aliasChars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

// GenerateRandomAlias 生成随机别名
func GenerateRandomAlias(length int) string {
	b := make([]byte, length)
	max := big.NewInt(int64(len(aliasChars)))
	for i := range b {
		num, err := rand.Int(rand.Reader, max)
		if err != nil {
			// Fallback or handle error - using simple rand for now as fallback
			log.Printf("Error generating crypto/rand: %v, using math/rand fallback", err)
			// Note: math/rand needs seeding (e.g., rand.Seed(time.Now().UnixNano())) if used globally
			// For simplicity here, just returning a fixed string on error
			return "aliaserror"
		}
		b[i] = aliasChars[num.Int64()]
	}
	return string(b)
}

// GetCaptcha generates and returns a new captcha
// @Summary 获取验证码
// @Description 生成一个新的图片验证码，返回验证码密钥和Base64编码的图片
// @Tags Authentication
// @Produce json
// @Success 200 {object} dto.StandardResponse{data=dto.CaptchaResponse} "成功响应"
// @Failure 500 {object} dto.StandardResponse "生成验证码失败"
// @Router /admin/auth/captcha [get]
func GetCaptcha(c *fiber.Ctx) error {
	// Load assets (consider loading once at startup)
	fontDir := "./assets/fonts" // Adjust path if needed
	bgDir := "./assets/backgrounds"
	fontFiles, _ := utils.LoadFilesFromDir(fontDir, []string{".ttf"})
	bgFiles, _ := utils.LoadFilesFromDir(bgDir, []string{".png", ".jpg"})

	cfg := utils.Config{
		Length:      4, // Keep captcha length reasonable
		Width:       120,
		Height:      40,
		CharSet:     "ABCDEFGHJKMNPQRSTUVWXYZ23456789", // Avoid confusing chars
		Sensitive:   false,
		Fonts:       fontFiles,
		Backgrounds: bgFiles,
		UseBgImage:  len(bgFiles) > 0,
		NumLines:    3,
		MaxAngle:    10,
	}

	result := utils.Generate(cfg)
	if result.Err != nil {
		return utils.ServerError(c, "生成验证码失败", result.Err)
	}

	captchaKey := uuid.NewString()

	// --- Development Log ---
	appConfig := config.LoadConfig() // Load config to check environment
	if appConfig.AppEnv == "development" {
		log.Printf("[DEV] Captcha Generated - Key: %s, Answer: %s", captchaKey, result.Answer)
	}
	// --- End Development Log ---

	// Store the answer in Redis using utils package
	if err := utils.StoreCaptcha(captchaKey, result.Answer); err != nil {
		return utils.ServerError(c, "存储验证码失败", err)
	}

	// Encode image to base64
	imgBase64 := base64.StdEncoding.EncodeToString(result.Image)
	imageDataURL := "data:image/png;base64," + imgBase64

	return utils.Success(c, "验证码获取成功", dto.CaptchaResponse{
		CaptchaKey: captchaKey,
		ImageData:  imageDataURL,
	})
}

// GetRecaptchaConfig 获取 reCAPTCHA 配置信息
// @Summary 获取 reCAPTCHA 配置
// @Description 获取 Google reCAPTCHA v2 的配置信息，包括是否启用和站点密钥
// @Tags Authentication
// @Produce json
// @Success 200 {object} dto.StandardResponse{data=dto.RecaptchaConfigResponse} "成功响应"
// @Router /admin/auth/recaptcha-config [get]
func GetRecaptchaConfig(c *fiber.Ctx) error {
	return utils.Success(c, "reCAPTCHA 配置获取成功", dto.RecaptchaConfigResponse{
		Enabled: utils.IsRecaptchaEnabled(),
		SiteKey: utils.GetRecaptchaSiteKey(),
	})
}

// Signup 处理管理员注册请求
// @Summary 管理员注册
// @Description 创建一个新的管理员账户
// @Tags Authentication
// @Accept json
// @Produce json
// @Param signup body dto.AuthSignupDTO true "注册信息 (邀请码根据配置决定是否必需)"
// @Success 200 {object} dto.StandardResponse{data=object{id=int}} "注册成功，返回用户ID"
// @Failure 400 {object} dto.StandardResponse "请求参数错误、验证码错误、邀请码错误或用户名/邮箱已存在"
// @Failure 500 {object} dto.StandardResponse "服务器内部错误"
// @Router /admin/auth/signup [post]
func Signup(c *fiber.Ctx) error {
	signupDTO := new(dto.AuthSignupDTO)
	if err := c.BodyParser(signupDTO); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}

	// 1. 基本字段验证
	// TODO: Use a proper validation library for better error messages
	if signupDTO.Username == "" || signupDTO.Password == "" || signupDTO.Captcha == "" || signupDTO.CaptchaKey == "" {
		return utils.BadRequest(c, "用户名、密码、验证码和密钥不能为空", nil)
	}

	// 三层验证逻辑开始
	// 第一层：验证图形验证码
	if !utils.VerifyCaptcha(signupDTO.CaptchaKey, signupDTO.Captcha) {
		return utils.BadRequest(c, "图形验证码错误", nil)
	}

	// 第二层：验证 Google reCAPTCHA v2 (如果启用)
	if utils.IsRecaptchaEnabled() {
		if signupDTO.RecaptchaToken == "" {
			return utils.BadRequest(c, "请完成 reCAPTCHA 验证", nil)
		}

		// 验证 reCAPTCHA token
		isValid, err := utils.VerifyRecaptcha(signupDTO.RecaptchaToken, c.IP())
		if err != nil {
			log.Printf("reCAPTCHA 验证失败: %v", err)
			return utils.BadRequest(c, "reCAPTCHA 验证失败，请重试", nil)
		}
		if !isValid {
			return utils.BadRequest(c, "reCAPTCHA 验证失败，请重新验证", nil)
		}
	}

	// 3. 邀请码逻辑 - 使用设置工具
	refereeValue := ""
	if utils.IsInvitationRequired() {
		if signupDTO.InvitationCode == "" {
			return utils.BadRequest(c, "邀请码不能为空", nil)
		}
		
		// 验证邀请码
		var invitation models.Invitation
		if err := database.DB.Where("code = ? AND state = 1", signupDTO.InvitationCode).First(&invitation).Error; err != nil {
			return utils.BadRequest(c, "邀请码无效或已过期", nil)
		}
		
		// 检查邀请码使用次数
		log.Printf("邀请码验证 - 邀请码ID: %d, 使用次数: %d, 限制次数: %d", invitation.ID, invitation.UsedCount, invitation.Limit)
		
		// 先检查邀请码自身限制
		if invitation.Limit > 0 && invitation.UsedCount >= invitation.Limit {
			log.Printf("邀请码验证失败 - 已达到自身限制")
			return utils.BadRequest(c, "邀请码已达到最大使用次数", nil)
		}
		
		// 注释掉系统全局配置检查，仅使用邀请码自身的限制
		/*
		maxUses := utils.GetInvitationCodeMaxUses()
		log.Printf("邀请码验证 - 系统全局限制: %d", maxUses)
		
		// 只有当maxUses > 0时才进行系统限制检查
		if maxUses > 0 && invitation.UsedCount >= uint(maxUses) {
			log.Printf("邀请码验证失败 - 已达到系统全局限制")
			return utils.BadRequest(c, "邀请码已达到系统允许的最大使用次数", nil)
		}
		*/
		
		log.Printf("邀请码验证通过")
		
		// 检查邀请码是否过期
		if invitation.IsExpired() {
			return utils.BadRequest(c, "邀请码已过期", nil)
		}
		
		// 更新邀请码使用次数
		database.DB.Model(&invitation).Update("used_count", invitation.UsedCount+1)
		
		refereeValue = signupDTO.InvitationCode
	} else {
		// 如果不需要邀请码，使用推荐人字段
		refereeValue = signupDTO.Referee
		if refereeValue == "" {
			refereeValue = "NONE"
		}
	}

	// 4. 检查用户名和邮箱是否已存在
	var existingUser models.AdminUser
	if err := database.DB.Where("username = ?", signupDTO.Username).First(&existingUser).Error; err == nil {
		return utils.BadRequest(c, "用户名已存在", nil)
	}
	if signupDTO.Email != "" {
		if err := database.DB.Where("email = ?", signupDTO.Email).First(&existingUser).Error; err == nil {
			return utils.BadRequest(c, "邮箱已被注册", nil)
		}
	}

	// 5. 哈希密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(signupDTO.Password), bcrypt.DefaultCost)
	if err != nil {
		return utils.ServerError(c, "密码处理失败", err)
	}

	// 6. 生成别名 (Alias)
	alias := GenerateRandomAlias(10) // Generate a 10-character alias

	// 7. 创建用户
	user := models.AdminUser{
		Username:        signupDTO.Username,
		Password:        string(hashedPassword),
		Alias:           alias,
		Name:            signupDTO.Username, // Name defaults to Username per description
		Email:           signupDTO.Email,
		Referee:         refereeValue, // Set based on invitation logic
		State:           1,            // Default to enabled
		LastedIPAddress: c.IP(),
		Notify:          1, // Default to notify enabled
	}

	result := database.DB.Create(&user)
	if result.Error != nil {
		return utils.ServerError(c, "注册失败，请稍后重试", result.Error)
	}

	// 为新用户分配默认角色
	// 创建用户-角色关联
	roleUser := models.AdminRoleUser{
		RoleID: 2, // 默认角色ID - 用户
		UserID: user.ID,
	}
	
	if err := database.DB.Create(&roleUser).Error; err != nil {
		log.Printf("为用户分配默认角色失败: %v", err)
		// 不返回错误，继续执行后续逻辑
	} else {
		log.Printf("已为用户 %s (ID: %d) 分配默认角色 (ID: %d)", user.Username, user.ID, 2)
	}

	// 8. 如果需要邮箱验证，发送验证邮件
	if utils.IsEmailVerificationRequired() && user.Email != "" {
		// 使用email包发送验证邮件
		go func() {
			// 获取验证邮件模板
			emailTemplate, err := utils.GetEmailTemplate("email_verification")
			if err != nil {
				log.Printf("无法加载邮箱验证模板: %v", err)
				return
			}
			
			// 生成验证链接
			verificationToken, _ := email.GenerateEmailVerificationToken(user.ID)
			verificationLink := fmt.Sprintf("%s/api/admin/auth/verify-email?token=%s", 
				config.LoadConfig().WebBaseURL, verificationToken)
			
			// 准备邮件数据
			emailData := email.EmailData{
				To:      []string{user.Email},
				Subject: "验证您的邮箱",
				Template: emailTemplate,
				Data: map[string]string{
					"Username":        user.Username,
					"VerificationLink": verificationLink,
					"ExpireMinutes":   fmt.Sprintf("%d", utils.GetEmailVerificationExpireMinutes()),
				},
				IsHTML: true,
			}
			
			// 发送邮件
			if err := email.SendEmail(emailData); err != nil {
				log.Printf("发送验证邮件失败: %v", err)
			}
		}()
	} else {
		// 如果不需要邮箱验证，发送欢迎邮件
		if user.Email != "" {
			go func() {
				// 获取欢迎邮件模板
				emailTemplate, err := utils.GetEmailTemplate("welcome")
				if err != nil {
					log.Printf("无法加载欢迎邮件模板: %v", err)
					return
				}
				
				// 准备邮件数据
				emailData := email.EmailData{
					To:      []string{user.Email},
					Subject: "欢迎加入",
					Template: emailTemplate,
					Data: map[string]string{
						"Username": user.Username,
					},
					IsHTML: true,
				}
				
				// 发送邮件
				if err := email.SendEmail(emailData); err != nil {
					log.Printf("发送欢迎邮件失败: %v", err)
				}
			}()
		}
	}

	return utils.Success(c, "注册成功", fiber.Map{"id": user.ID})
}

// Login 处理管理员登录请求
// @Summary 管理员登录
// @Description 使用用户名、密码和验证码登录并获取JWT令牌
// @Tags Authentication
// @Accept json
// @Produce json
// @Param login body dto.AuthLoginDTO true "登录凭证 (包含验证码)"
// @Success 200 {object} dto.StandardResponse{data=dto.AuthResponse} "登录成功，返回JWT令牌"
// @Failure 400 {object} dto.StandardResponse "请求参数错误或验证码错误"
// @Failure 401 {object} dto.StandardResponse "用户名或密码错误或账户被禁用"
// @Failure 500 {object} dto.StandardResponse "服务器内部错误或令牌生成失败"
// @Router /admin/auth/login [post]
func Login(c *fiber.Ctx) error {
	loginDTO := new(dto.AuthLoginDTO)
	if err := c.BodyParser(loginDTO); err != nil {
		return utils.BadRequest(c, "无效的登录数据", err)
	}

	// 1. 基本字段验证
	// TODO: Use a proper validation library
	if loginDTO.Username == "" || loginDTO.Password == "" || loginDTO.Captcha == "" || loginDTO.CaptchaKey == "" {
		return utils.BadRequest(c, "用户名、密码和验证码信息不能为空", nil)
	}

	// 三层验证逻辑开始
	// 第一层：验证图形验证码
	if !utils.VerifyCaptcha(loginDTO.CaptchaKey, loginDTO.Captcha) {
		return utils.BadRequest(c, "图形验证码错误", nil)
	}

	// 第二层：验证 Google reCAPTCHA v2 (如果启用)
	if utils.IsRecaptchaEnabled() {
		if loginDTO.RecaptchaToken == "" {
			return utils.BadRequest(c, "请完成 reCAPTCHA 验证", nil)
		}

		// 验证 reCAPTCHA token
		isValid, err := utils.VerifyRecaptcha(loginDTO.RecaptchaToken, c.IP())
		if err != nil {
			log.Printf("reCAPTCHA 验证失败: %v", err)
			return utils.BadRequest(c, "reCAPTCHA 验证失败，请重试", nil)
		}
		if !isValid {
			return utils.BadRequest(c, "reCAPTCHA 验证失败，请重新验证", nil)
		}
	}

	// 第三层：验证用户名和密码
	var user models.AdminUser
	if err := database.DB.Where("username = ?", loginDTO.Username).First(&user).Error; err != nil {
		// 避免透露用户名是否存在，统一返回认证失败
		log.Printf("Login attempt failed for user '%s': User not found or DB error: %v", loginDTO.Username, err)
		return utils.Unauthorized(c, "用户名或密码错误", nil)
	}

	// 检查用户状态
	if user.State != 1 {
		log.Printf("Login attempt failed for user '%s': Account disabled (state=%d)", loginDTO.Username, user.State)
		return utils.Unauthorized(c, "账户已被禁用", nil)
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(loginDTO.Password)); err != nil {
		log.Printf("Login attempt failed for user '%s': Invalid password", loginDTO.Username)
		return utils.Unauthorized(c, "用户名或密码错误", nil)
	}

	// 6. 检查网站是否处于维护模式
	isSiteClosed := utils.IsSiteClosed()
	if isSiteClosed {
		// 检查是否为管理员登录请求
		isAdminLogin := loginDTO.IsAdminLogin
		
		// 检查用户是否为管理员
		isAdmin := false
		
		// 检查是否为超级管理员 (ID=1)
		if user.ID == 1 {
			isAdmin = true
		} else {
			// 查询用户角色
			var roles []models.Role
			err := database.DB.Model(&user).Association("Roles").Find(&roles)
			if err == nil {
				for _, role := range roles {
					// 检查角色名称或标识是否包含管理员关键字
					roleName := strings.ToLower(role.Name)
					roleSlug := strings.ToLower(role.Slug)
					
					if role.ID == 1 || // ID为1的角色通常是管理员角色
						strings.Contains(roleName, "admin") || 
						strings.Contains(roleSlug, "admin") ||
						strings.Contains(roleName, "管理") {
						isAdmin = true
						break
					}
				}
			}
		}
		
		// 如果网站处于维护模式且不是管理员登录，则拒绝访问
		if !isAdmin || !isAdminLogin {
			log.Printf("Login attempt failed for user '%s': Site is in maintenance mode and user is not admin", loginDTO.Username)
			return utils.Forbidden(c, "网站维护中，仅管理员可登录", nil)
		}
		
		log.Printf("Admin user '%s' (ID=%d) logged in during maintenance mode", user.Username, user.ID)
	}

	// --- 7. 生成JWT令牌 ---
	t, err := utils.GenerateToken(user.ID, user.Username, user.Name)
	log.Println("生成的token:", t)
	log.Println("用户 id:", user.ID)
	if err != nil {
		log.Printf("JWT signing error for user '%s': %v", loginDTO.Username, err)
		return utils.ServerError(c, "生成认证令牌失败", err)
	}

	// --- 8. 更新最后登录信息 (可选) ---
	now := time.Now()
	updateResult := database.DB.Model(&user).Updates(models.AdminUser{
		LastedIPAddress: c.IP(),
		LastedAt:        &now,
	})
	if updateResult.Error != nil {
		// 记录错误但通常不应阻止登录成功
		log.Printf("Failed to update last login info for user '%s': %v", loginDTO.Username, updateResult.Error)
	}

	// --- 9. 检查是否需要强制绑定 2FA ---
	requireTwoFactor := false

	// 检查用户是否为新用户且未设置 2FA
	if !user.TwoFactorEnabled && user.TwoFactorSetupAt == nil {
		// 检查用户是否为首次登录（可以通过 LastedAt 字段判断）
		if user.LastedAt == nil {
			requireTwoFactor = true
			log.Printf("User '%s' (ID=%d) requires 2FA setup on first login", user.Username, user.ID)
		}
	}

	// --- 10. 返回JWT令牌和用户信息 ---
	// 构建用户信息响应
	userInfo := fiber.Map{
		"id":                  user.ID,
		"username":            user.Username,
		"name":                user.Name,
		"email":               user.Email,
		"avatar":              user.Avatar,
		"is_admin":            user.ID == 1, // 简单判断是否为超级管理员
		"two_factor_enabled":  user.TwoFactorEnabled,
		"require_two_factor":  requireTwoFactor, // 是否需要强制设置 2FA
	}

	responseMessage := "登录成功"
	if requireTwoFactor {
		responseMessage = "登录成功，请设置双因素认证"
	}

	return utils.Success(c, responseMessage, fiber.Map{
		"token":     t,
		"user_info": userInfo,
	})
}

// ForgotPassword 处理管理员忘记密码请求
// @Summary 忘记密码
// @Description 处理用户忘记密码请求，向用户邮箱发送重置密码链接
// @Tags Authentication
// @Accept json
// @Produce json
// @Param forgotPassword body dto.AuthForgotPasswordDTO true "忘记密码请求信息"
// @Success 200 {object} dto.StandardResponse "成功发送密码重置邮件"
// @Failure 400 {object} dto.StandardResponse "请求参数错误或验证码错误"
// @Failure 404 {object} dto.StandardResponse "用户名或邮箱不匹配"
// @Failure 500 {object} dto.StandardResponse "服务器内部错误或邮件发送失败"
// @Router /admin/auth/forgot [post]
func ForgotPassword(c *fiber.Ctx) error {
	forgotDTO := new(dto.AuthForgotPasswordDTO)
	if err := c.BodyParser(forgotDTO); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}

	// 1. 基本字段验证
	if forgotDTO.Username == "" || forgotDTO.Email == "" || forgotDTO.Captcha == "" || forgotDTO.CaptchaKey == "" {
		return utils.BadRequest(c, "用户名、邮箱、验证码和密钥不能为空", nil)
	}

	// 三层验证逻辑开始
	// 第一层：验证图形验证码
	if !utils.VerifyCaptcha(forgotDTO.CaptchaKey, forgotDTO.Captcha) {
		return utils.BadRequest(c, "图形验证码错误", nil)
	}

	// 第二层：验证 Google reCAPTCHA v2 (如果启用)
	if utils.IsRecaptchaEnabled() {
		if forgotDTO.RecaptchaToken == "" {
			return utils.BadRequest(c, "请完成 reCAPTCHA 验证", nil)
		}

		// 验证 reCAPTCHA token
		isValid, err := utils.VerifyRecaptcha(forgotDTO.RecaptchaToken, c.IP())
		if err != nil {
			log.Printf("reCAPTCHA 验证失败: %v", err)
			return utils.BadRequest(c, "reCAPTCHA 验证失败，请重试", nil)
		}
		if !isValid {
			return utils.BadRequest(c, "reCAPTCHA 验证失败，请重新验证", nil)
		}
	}

	// 3. 查找用户
	var user models.AdminUser
	if err := database.DB.Where("username = ? AND email = ?", forgotDTO.Username, forgotDTO.Email).First(&user).Error; err != nil {
		log.Printf("Password reset attempt failed: Username '%s' with email '%s' not found or DB error: %v", 
			forgotDTO.Username, forgotDTO.Email, err)
		return utils.NotFound(c, "用户名或邮箱不匹配")
	}

	// 4. 检查用户状态
	if user.State != 1 {
		log.Printf("Password reset attempt failed for user '%s': Account disabled (state=%d)", 
			forgotDTO.Username, user.State)
		return utils.BadRequest(c, "账户已被禁用，无法重置密码", nil)
	}

	// 5. 生成密码重置令牌
	resetToken, err := email.GeneratePasswordResetToken(user.ID)
	if err != nil {
		log.Printf("Generate reset token error for user '%s': %v", forgotDTO.Username, err)
		return utils.ServerError(c, "生成重置令牌失败", err)
	}

	// 6. 构建重置链接
	cfg := config.LoadConfig()
	resetLink := cfg.WebBaseURL + "/api/admin/auth/reset?token=" + resetToken

	// 7. 准备邮件内容
	emailTemplate := email.GetPasswordResetEmailTemplate()
	emailData := email.EmailData{
		To:       []string{user.Email},
		Subject:  "密码重置请求",
		Template: emailTemplate,
		Data: map[string]string{
			"Username":  user.Username,
			"ResetLink": resetLink,
		},
		IsHTML: true,
	}

	// 8. 发送邮件
	if err := queue.EnqueueEmailTask(emailData); err != nil {
		log.Printf("Send reset email error for user '%s': %v", forgotDTO.Username, err)
		return utils.ServerError(c, "发送重置邮件失败", err)
	}

	// 9. 返回成功响应
	return utils.Success(c, "密码重置邮件已发送至您的邮箱，请查收", nil)
}

// ResetPassword 处理管理员重置密码请求
// @Summary 重置密码
// @Description 使用重置令牌重置用户密码
// @Tags Authentication
// @Accept json
// @Produce json
// @Param resetPassword body dto.AuthResetPasswordDTO true "重置密码请求信息"
// @Success 200 {object} dto.StandardResponse "密码重置成功"
// @Failure 400 {object} dto.StandardResponse "请求参数错误或令牌无效"
// @Failure 404 {object} dto.StandardResponse "用户不存在"
// @Failure 500 {object} dto.StandardResponse "服务器内部错误"
// @Router /admin/auth/reset [post]
func ResetPassword(c *fiber.Ctx) error {
	resetDTO := new(dto.AuthResetPasswordDTO)
	if err := c.BodyParser(resetDTO); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}

	// 1. 基本字段验证
	if resetDTO.Token == "" || resetDTO.NewPassword == "" {
		return utils.BadRequest(c, "令牌和新密码不能为空", nil)
	}

	// 2. 验证令牌并获取用户ID
	userID, err := email.VerifyPasswordResetToken(resetDTO.Token)
	if err != nil {
		log.Printf("Invalid reset token: %v", err)
		return utils.BadRequest(c, "重置令牌无效或已过期", nil)
	}

	// 3. 查找用户
	var user models.AdminUser
	if err := database.DB.First(&user, userID).Error; err != nil {
		log.Printf("User not found for reset password, ID: %d, Error: %v", userID, err)
		return utils.NotFound(c, "用户不存在")
	}

	// 4. 检查用户状态
	if user.State != 1 {
		log.Printf("Password reset failed for user ID %d: Account disabled (state=%d)", userID, user.State)
		return utils.BadRequest(c, "账户已被禁用，无法重置密码", nil)
	}

	// 5. 哈希新密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(resetDTO.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		return utils.ServerError(c, "密码处理失败", err)
	}

	// 6. 更新密码
	updateResult := database.DB.Model(&user).Updates(models.AdminUser{
		Password: string(hashedPassword),
	})
	if updateResult.Error != nil {
		log.Printf("Password update failed for user ID %d: %v", userID, updateResult.Error)
		return utils.ServerError(c, "密码更新失败", updateResult.Error)
	}

	// 7. 返回成功响应
	return utils.Success(c, "密码重置成功，请使用新密码登录", nil)
}

// VerifyToken 验证JWT令牌的有效性
// @Summary 验证Token
// @Description 验证JWT令牌是否有效
// @Tags Authentication
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Success 200 {object} dto.StandardResponse{data=object{valid=boolean}} "验证结果"
// @Failure 401 {object} dto.StandardResponse "令牌无效"
// @Failure 500 {object} dto.StandardResponse "服务器内部错误"
// @Router /admin/auth/verify [get]
func VerifyToken(c *fiber.Ctx) error {
	// 从请求头中获取令牌
	authHeader := c.Get("Authorization")
	if authHeader == "" {
		// 如果没有提供令牌，则返回令牌无效
		return utils.Success(c, "验证完成", fiber.Map{"valid": false})
	}

	// 预期格式为 "Bearer {token}"
	tokenString := ""
	if len(authHeader) > 7 && authHeader[:7] == "Bearer " {
		tokenString = authHeader[7:]
	} else {
		tokenString = authHeader // 直接尝试使用提供的值
	}

	// 验证令牌
	_, err := utils.ParseToken(tokenString)
	if err != nil {
		log.Printf("Invalid token: %v", err)
		return utils.Success(c, "验证完成", fiber.Map{"valid": false})
	}

	// 令牌有效
	return utils.Success(c, "验证完成", fiber.Map{"valid": true})
}

// GetProfile 获取当前用户信息
// @Summary 获取用户信息
// @Description 获取当前登录用户的详细信息包括权限
// @Tags 认证管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} dto.StandardResponse{data=dto.ProfileResponse}
// @Failure 401 {object} dto.StandardResponse "未授权"
// @Router /auth/profile [get]
func GetProfile(c *fiber.Ctx) error {
	// 获取当前用户信息
	currentUser, err := utils.GetCurrentUserFromContext(c)
	if err != nil {
		return utils.Unauthorized(c, "获取用户信息失败", err)
	}

	// 获取用户角色
	var roles []models.Role
	if err := database.DB.Model(&currentUser).Association("Roles").Find(&roles); err != nil {
		return utils.ServerError(c, "获取用户角色失败", err)
	}

	// 构建角色列表
	var roleList []dto.RoleItem
	var roleIDs []uint64
	var roleSlugs []string
	for _, role := range roles {
		roleList = append(roleList, dto.RoleItem{
			ID:   role.ID,
			Name: role.Name,
			Slug: role.GuardName,
		})
		roleIDs = append(roleIDs, role.ID)
		roleSlugs = append(roleSlugs, role.GuardName)
	}

	// 获取用户权限
	permissions, err := utils.GetUserPermissions(currentUser.ID)
	if err != nil {
		return utils.ServerError(c, "获取用户权限失败", err)
	}

	// 构建用户信息响应
	response := dto.ProfileResponse{
		ID:          currentUser.ID,
		Username:    currentUser.Username,
		Name:        currentUser.Name,
		Email:       currentUser.Email,
		Avatar:      currentUser.Avatar,
		Roles:       roleList,
		Permissions: permissions,
		RoleSlugs:   roleSlugs,
	}

	return utils.Success(c, "获取用户信息成功", response)
}

// UpdateProfile 更新当前用户信息
// @Summary 更新用户信息
// @Description 更新当前登录用户的个人信息
// @Tags 认证管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param profile body dto.UpdateProfileDTO true "用户信息"
// @Success 200 {object} dto.StandardResponse{data=dto.ProfileResponse}
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 401 {object} dto.StandardResponse "未授权"
// @Router /auth/profile [put]
func UpdateProfile(c *fiber.Ctx) error {
	// 获取当前用户信息
	currentUser, err := utils.GetCurrentUserFromContext(c)
	if err != nil {
		return utils.Unauthorized(c, "获取用户信息失败", err)
	}

	// 解析请求体
	var req dto.UpdateProfileDTO
	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}

	// 验证请求数据
	if err := utils.ValidateStruct(req); err != nil {
		return utils.BadRequest(c, "数据验证失败", err)
	}

	// 更新用户信息
	currentUser.Name = req.Name
	if req.Email != "" && req.Email != currentUser.Email {
		// 检查邮箱是否已被其他用户使用
		var count int64
		database.DB.Model(&models.AdminUser{}).Where("email = ? AND id != ?", req.Email, currentUser.ID).Count(&count)
		if count > 0 {
			return utils.BadRequest(c, "邮箱已被其他用户使用", nil)
		}
		currentUser.Email = req.Email
	}
	
	// 更新头像
	if req.Avatar != "" {
		currentUser.Avatar = req.Avatar
	}

	// 保存更新
	if err := database.DB.Save(&currentUser).Error; err != nil {
		return utils.ServerError(c, "更新用户信息失败", err)
	}

	// 获取更新后的用户角色
	var roles []models.Role
	if err := database.DB.Model(&currentUser).Association("Roles").Find(&roles); err != nil {
		return utils.ServerError(c, "获取用户角色失败", err)
	}

	// 构建角色列表
	var roleList []dto.RoleItem
	var roleSlugs []string
	for _, role := range roles {
		roleList = append(roleList, dto.RoleItem{
			ID:   role.ID,
			Name: role.Name,
			Slug: role.GuardName,
		})
		roleSlugs = append(roleSlugs, role.GuardName)
	}

	// 获取用户权限
	permissions, err := utils.GetUserPermissions(currentUser.ID)
	if err != nil {
		return utils.ServerError(c, "获取用户权限失败", err)
	}

	// 构建用户信息响应
	response := dto.ProfileResponse{
		ID:          currentUser.ID,
		Username:    currentUser.Username,
		Name:        currentUser.Name,
		Email:       currentUser.Email,
		Avatar:      currentUser.Avatar,
		Roles:       roleList,
		Permissions: permissions,
		RoleSlugs:   roleSlugs,
	}

	return utils.Success(c, "更新用户信息成功", response)
}

// UploadAvatar 处理头像上传
// @Summary 上传头像
// @Description 上传用户头像并返回URL
// @Tags Authentication
// @Accept multipart/form-data
// @Produce json
// @Security BearerAuth
// @Param file formData file true "头像图片文件"
// @Success 200 {object} dto.StandardResponse{data=dto.AvatarResponse} "上传成功，返回头像URL"
// @Failure 400 {object} dto.StandardResponse "请求参数错误或文件类型/大小不符合要求"
// @Failure 401 {object} dto.StandardResponse "未授权"
// @Failure 500 {object} dto.StandardResponse "服务器内部错误"
// @Router /admin/upload/avatar [post]
func UploadAvatar(c *fiber.Ctx) error {
	// 获取当前用户信息
	currentUser, err := utils.GetCurrentUserFromContext(c)
	if err != nil {
		return utils.Unauthorized(c, "获取用户信息失败", err)
	}

	// 获取上传的文件
	file, err := c.FormFile("file")
	if err != nil {
		return utils.BadRequest(c, "文件上传失败", err)
	}

	// 检查文件类型
	fileExt := filepath.Ext(file.Filename)
	if fileExt != ".jpg" && fileExt != ".jpeg" && fileExt != ".png" && fileExt != ".gif" {
		return utils.BadRequest(c, "不支持的文件类型，仅支持jpg、jpeg、png和gif格式", nil)
	}

	// 检查文件大小
	if file.Size > 2*1024*1024 { // 2MB
		return utils.BadRequest(c, "文件大小不能超过2MB", nil)
	}

	// 确保上传目录存在
	uploadDir := filepath.Join("uploads", "avatars")
	if err := os.MkdirAll(uploadDir, 0755); err != nil {
		log.Printf("创建上传目录失败: %v", err)
		return utils.ServerError(c, "创建上传目录失败", err)
	}

	// 生成唯一文件名
	fileName := fmt.Sprintf("avatar_%d_%s%s", currentUser.ID, uuid.New().String()[:8], fileExt)
	filePath := filepath.Join(uploadDir, fileName)

	// 保存文件
	if err := c.SaveFile(file, filePath); err != nil {
		log.Printf("保存文件失败: %v", err)
		return utils.ServerError(c, "保存文件失败", err)
	}

	// 构建文件URL
	fileURL := "/static/uploads/avatars/" + fileName

	// 更新用户头像URL
	currentUser.Avatar = fileURL
	if err := database.DB.Save(&currentUser).Error; err != nil {
		log.Printf("更新用户头像失败: %v", err)
		return utils.ServerError(c, "更新用户头像失败", err)
	}

	// 返回成功响应
	return utils.Success(c, "头像上传成功", dto.AvatarResponse{
		URL: fileURL,
	})
}

// VerifyEmail 处理邮箱验证请求
// @Summary 验证邮箱
// @Description 通过验证链接验证用户邮箱
// @Tags Authentication
// @Accept json
// @Produce json
// @Param token query string true "验证令牌"
// @Success 200 {object} dto.StandardResponse "邮箱验证成功"
// @Failure 400 {object} dto.StandardResponse "请求参数错误或令牌无效"
// @Failure 500 {object} dto.StandardResponse "服务器内部错误"
// @Router /admin/auth/verify-email [get]
func VerifyEmail(c *fiber.Ctx) error {
	// 获取令牌
	token := c.Query("token")
	if token == "" {
		return utils.BadRequest(c, "缺少验证令牌", nil)
	}

	// 验证令牌
	userID, err := email.VerifyEmailVerificationToken(token)
	if err != nil {
		return utils.BadRequest(c, "验证令牌无效或已过期", err)
	}

	// 更新用户邮箱验证状态
	db := database.DB
	result := db.Model(&models.AdminUser{}).Where("id = ?", userID).Update("email_verified", true)
	if result.Error != nil {
		return utils.ServerError(c, "更新邮箱验证状态失败", result.Error)
	}

	// 使用模板工具加载验证成功页面模板
	content, err := utils.GetEmailTemplate("email_verified_page")
	if err != nil {
		// 如果模板不存在，返回简单的成功消息
		return utils.Success(c, "邮箱验证成功", nil)
	}

	// 返回HTML页面
	return c.Status(200).Type("html").SendString(content)
}

// GetCurrentUser 获取当前用户完整信息，包括角色和权限
// @Summary 获取当前用户完整信息
// @Description 获取当前已登录用户的完整信息，包括基本用户信息、角色和权限
// @Tags Authentication
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} dto.StandardResponse "成功获取当前用户信息"
// @Failure 401 {object} dto.StandardResponse "未认证或认证已过期"
// @Failure 500 {object} dto.StandardResponse "服务器内部错误"
// @Router /admin/auth/current-user [get]
func GetCurrentUser(c *fiber.Ctx) error {
	// 获取当前用户
	user, err := utils.GetCurrentUserFromContext(c)
	if err != nil {
		log.Printf("GetCurrentUser: 获取当前用户失败: %v", err)
		return utils.Unauthorized(c, "未找到有效的用户信息", err)
	}

	log.Printf("GetCurrentUser: 获取用户ID=%d的完整信息", user.ID)

	// 加载用户角色
	var roles []models.Role
	if err := database.DB.Model(&user).Association("Roles").Find(&roles); err != nil {
		log.Printf("GetCurrentUser: 加载用户角色失败: %v", err)
		// 不返回错误，继续处理
	} else {
		log.Printf("GetCurrentUser: 用户拥有%d个角色", len(roles))
	}

	// 获取用户通过角色拥有的所有权限
	var permissions []models.Permission
	if len(roles) > 0 {
		var roleIDs []uint64
		for _, role := range roles {
			roleIDs = append(roleIDs, role.ID)
		}
		
		// 查询角色关联的所有权限
		if err := database.DB.Raw(`
			SELECT DISTINCT p.* FROM admin_permissions p
			JOIN admin_role_permissions rp ON p.id = rp.permission_id
			WHERE rp.role_id IN ?
		`, roleIDs).Find(&permissions).Error; err != nil {
			log.Printf("GetCurrentUser: 查询权限失败: %v", err)
		} else {
			log.Printf("GetCurrentUser: 用户通过角色拥有%d个权限", len(permissions))
		}
	}

	// 获取用户可访问的菜单
	var menus []models.Menu
	if len(roles) > 0 {
		var roleIDs []uint64
		for _, role := range roles {
			roleIDs = append(roleIDs, role.ID)
		}
		
		// 查询角色关联的所有菜单
		if err := database.DB.Raw(`
			SELECT DISTINCT m.* FROM admin_menu m
			JOIN admin_role_menu rm ON m.id = rm.menu_id
			WHERE rm.role_id IN ?
			ORDER BY m.order ASC
		`, roleIDs).Find(&menus).Error; err != nil {
			log.Printf("GetCurrentUser: 查询菜单失败: %v", err)
		} else {
			log.Printf("GetCurrentUser: 用户通过角色可访问%d个菜单项", len(menus))
		}
	}

	// 组织用户详细信息
	var lastedAt string
	if user.LastedAt != nil {
		lastedAt = user.LastedAt.Format("2006-01-02 15:04:05")
	}

	userInfo := fiber.Map{
		"id":              user.ID,
		"username":        user.Username,
		"name":            user.Name,
		"email":           user.Email,
		"avatar":          user.Avatar,
		"referee":         user.Referee,
		"notify":          user.Notify,
		"lasted_ipaddress": user.LastedIPAddress,
		"lasted_at":        lastedAt,
		"state":            user.State,
		"created_at":       user.CreatedAt.Format("2006-01-02 15:04:05"),
		"updated_at":       user.UpdatedAt.Format("2006-01-02 15:04:05"),
		"is_admin":         utils.IsAdmin(c),
	}

	// 构建角色信息
	var roleInfo []fiber.Map
	for _, role := range roles {
		roleInfo = append(roleInfo, fiber.Map{
			"id":         role.ID,
			"name":       role.Name,
			"slug":       role.Slug,
			"guard_name": role.GuardName,
			"remark":     role.Remark,
		})
	}

	// 构建权限信息
	var permissionInfo []fiber.Map
	for _, perm := range permissions {
		permissionInfo = append(permissionInfo, fiber.Map{
			"id":          perm.ID,
			"name":        perm.Name,
			"slug":        perm.Slug,
			"http_method": perm.HttpMethod,
			"http_path":   perm.HttpPath,
			"parent_id":   perm.ParentID,
			"order":       perm.Order,
		})
	}

	// 构建菜单信息
	var menuInfo []fiber.Map
	for _, menu := range menus {
		menuInfo = append(menuInfo, fiber.Map{
			"id":        menu.ID,
			"parent_id": menu.ParentID,
			"order":     menu.Order,
			"title":     menu.Title,
			"icon":      menu.Icon,
			"uri":       menu.URI,
		})
	}

	return utils.Success(c, "获取当前用户信息成功", fiber.Map{
		"user":        userInfo,
		"roles":       roleInfo,
		"permissions": permissionInfo,
		"menus":       menuInfo,
	})
}
