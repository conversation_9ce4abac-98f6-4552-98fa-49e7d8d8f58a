package handlers

import (
	"go-fiber-api/api/dto"
	"go-fiber-api/database"
	"go-fiber-api/models"
	"go-fiber-api/utils"
	"log"
	"strconv"
	"time"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

// 默认设置
var defaultSettings = map[string]string{
	"site_name":        "Go Fiber API",
	"site_keywords":    "Go,Fiber,API",
	"site_description": "Go Fiber API是一个基于Fiber框架的API服务",
	"site_logo":        "",
	"site_favicon":     "",
	"site_footer":      "Copyright © 2023 Go Fiber API",
	"site_notice":      "",
	"site_closed":      "false",
	"closed_message":   "网站维护中，请稍后再试",
	"register_closed":  "false",
	"mail_host":        "",
	"mail_port":        "587",
	"mail_username":    "",
	"mail_password":    "",
	"mail_from_name":   "Go Fiber API",
	"mail_from_email":  "",
}

// 获取设置值
func getSetting(key string, defaultValue string) string {
	var setting models.Settings
	result := database.DB.Where("`key` = ?", key).First(&setting)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			// 如果设置不存在，则创建
			setting = models.Settings{
				Key:   key,
				Value: defaultValue,
			}
			database.DB.Create(&setting)
			return defaultValue
		}
		return defaultValue
	}
	return setting.Value
}

// 设置值
func setSetting(key string, value string) error {
	log.Printf("设置 %s = %s", key, value)
	
	var setting models.Settings
	result := database.DB.Where("`key` = ?", key).First(&setting)
	
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			// 如果设置不存在，则创建
			log.Printf("创建新设置: %s = %s", key, value)
			setting = models.Settings{
				Key:   key,
				Value: value,
			}
			result = database.DB.Create(&setting)
			if result.Error != nil {
				log.Printf("创建设置失败: %v", result.Error)
				return result.Error
			}
			if result.RowsAffected == 0 {
				log.Printf("创建设置未影响任何行: %s", key)
				return fiber.NewError(fiber.StatusInternalServerError, "创建设置失败")
			}
			log.Printf("创建设置成功: %s = %s, ID: %d", key, value, setting.ID)
			return nil
		}
		log.Printf("查询设置失败: %v", result.Error)
		return result.Error
	}
	
	// 如果设置存在，则更新
	log.Printf("更新设置: %s = %s, 原值: %s, ID: %d", key, value, setting.Value, setting.ID)
	setting.Value = value
	result = database.DB.Save(&setting)
	if result.Error != nil {
		log.Printf("更新设置失败: %v", result.Error)
		return result.Error
	}
	if result.RowsAffected == 0 {
		log.Printf("更新设置未影响任何行: %s", key)
		return fiber.NewError(fiber.StatusInternalServerError, "更新设置失败")
	}
	log.Printf("更新设置成功: %s = %s, ID: %d", key, value, setting.ID)
	return nil
}

// GetSettings 获取网站设置
// @Summary 获取网站设置
// @Description 获取所有网站设置
// @Tags 网站设置
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} dto.StandardResponse{data=dto.SettingsResponse}
// @Router /admin/settings [get]
func GetSettings(c *fiber.Ctx) error {
	// 检查数据库连接
	if database.DB == nil {
		log.Printf("数据库连接异常: database.DB为空")
		return utils.ServerError(c, "数据库连接异常", nil)
	}
	
	// 从数据库获取所有设置
	settings := dto.SettingsResponse{
		SiteName:        getSetting("site_name", defaultSettings["site_name"]),
		SiteKeywords:    getSetting("site_keywords", defaultSettings["site_keywords"]),
		SiteDescription: getSetting("site_description", defaultSettings["site_description"]),
		SiteLogo:        getSetting("site_logo", defaultSettings["site_logo"]),
		SiteFavicon:     getSetting("site_favicon", defaultSettings["site_favicon"]),
		SiteFooter:      getSetting("site_footer", defaultSettings["site_footer"]),
		SiteNotice:      getSetting("site_notice", defaultSettings["site_notice"]),
		SiteClosed:      getSetting("site_closed", defaultSettings["site_closed"]) == "true",
		ClosedMessage:   getSetting("closed_message", defaultSettings["closed_message"]),
		RegisterClosed:  getSetting("register_closed", defaultSettings["register_closed"]) == "true",
		MailHost:        getSetting("mail_host", defaultSettings["mail_host"]),
		MailPort:        func() int {
			p, err := strconv.Atoi(getSetting("mail_port", defaultSettings["mail_port"]))
			if err != nil {
				p, _ = strconv.Atoi(defaultSettings["mail_port"])
			}
			return p
		}(),
		MailUsername:   getSetting("mail_username", defaultSettings["mail_username"]),
		MailPassword:   getSetting("mail_password", defaultSettings["mail_password"]),
		MailFromName:   getSetting("mail_from_name", defaultSettings["mail_from_name"]),
		MailFromEmail:  getSetting("mail_from_email", defaultSettings["mail_from_email"]),
	}
	
	return utils.Success(c, "获取网站设置成功", settings)
}

// UpdateSettings 更新网站设置
// @Summary 更新网站设置
// @Description 更新网站设置
// @Tags 网站设置
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param settings body dto.SettingsUpdateRequest true "网站设置信息"
// @Success 200 {object} dto.StandardResponse{data=dto.SettingsResponse}
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Router /admin/settings [put]
func UpdateSettings(c *fiber.Ctx) error {
	
	// 检查数据库连接
	if database.DB == nil {
		log.Printf("数据库连接异常: database.DB为空")
		return utils.ServerError(c, "数据库连接异常", nil)
	}
	
	// 解析请求体
	var req dto.SettingsUpdateRequest
	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}
	
	// 验证请求数据
	if err := utils.ValidateStruct(req); err != nil {
		return utils.BadRequest(c, "数据验证失败", err)
	}
	
	log.Printf("接收到设置更新请求: %+v", req)
	
	// 更新设置
	var updateErrors []error
	
	if req.SiteName != nil {
		if err := setSetting("site_name", *req.SiteName); err != nil {
			updateErrors = append(updateErrors, err)
		}
	}
	
	if req.SiteKeywords != nil {
		if err := setSetting("site_keywords", *req.SiteKeywords); err != nil {
			updateErrors = append(updateErrors, err)
		}
	}
	
	if req.SiteDescription != nil {
		if err := setSetting("site_description", *req.SiteDescription); err != nil {
			updateErrors = append(updateErrors, err)
		}
	}
	
	if req.SiteLogo != nil {
		if err := setSetting("site_logo", *req.SiteLogo); err != nil {
			updateErrors = append(updateErrors, err)
		}
	}
	
	if req.SiteFavicon != nil {
		if err := setSetting("site_favicon", *req.SiteFavicon); err != nil {
			updateErrors = append(updateErrors, err)
		}
	}
	
	if req.SiteFooter != nil {
		if err := setSetting("site_footer", *req.SiteFooter); err != nil {
			updateErrors = append(updateErrors, err)
		}
	}
	
	if req.SiteNotice != nil {
		if err := setSetting("site_notice", *req.SiteNotice); err != nil {
			updateErrors = append(updateErrors, err)
		}
	}
	
	if req.SiteClosed != nil {
		value := "false"
		if *req.SiteClosed {
			value = "true"
		}
		if err := setSetting("site_closed", value); err != nil {
			updateErrors = append(updateErrors, err)
		}
	}
	
	if req.ClosedMessage != nil {
		if err := setSetting("closed_message", *req.ClosedMessage); err != nil {
			updateErrors = append(updateErrors, err)
		}
	}
	
	if req.RegisterClosed != nil {
		value := "false"
		if *req.RegisterClosed {
			value = "true"
		}
		if err := setSetting("register_closed", value); err != nil {
			updateErrors = append(updateErrors, err)
		}
	}
	
	if req.MailHost != nil {
		if err := setSetting("mail_host", *req.MailHost); err != nil {
			updateErrors = append(updateErrors, err)
		}
	}
	
	if req.MailPort != nil {
		if err := setSetting("mail_port", strconv.Itoa(*req.MailPort)); err != nil {
			updateErrors = append(updateErrors, err)
		}
	}
	
	if req.MailUsername != nil {
		if err := setSetting("mail_username", *req.MailUsername); err != nil {
			updateErrors = append(updateErrors, err)
		}
	}
	
	if req.MailPassword != nil {
		if err := setSetting("mail_password", *req.MailPassword); err != nil {
			updateErrors = append(updateErrors, err)
		}
	}
	
	if req.MailFromName != nil {
		if err := setSetting("mail_from_name", *req.MailFromName); err != nil {
			updateErrors = append(updateErrors, err)
		}
	}
	
	if req.MailFromEmail != nil {
		if err := setSetting("mail_from_email", *req.MailFromEmail); err != nil {
			updateErrors = append(updateErrors, err)
		}
	}
	
	// 检查是否有更新错误
	if len(updateErrors) > 0 {
		log.Printf("设置更新过程中发生错误: %v", updateErrors)
		return utils.ServerError(c, "部分设置更新失败", updateErrors[0])
	}
	
	// 获取更新后的设置
	settings := dto.SettingsResponse{
		SiteName:        getSetting("site_name", defaultSettings["site_name"]),
		SiteKeywords:    getSetting("site_keywords", defaultSettings["site_keywords"]),
		SiteDescription: getSetting("site_description", defaultSettings["site_description"]),
		SiteLogo:        getSetting("site_logo", defaultSettings["site_logo"]),
		SiteFavicon:     getSetting("site_favicon", defaultSettings["site_favicon"]),
		SiteFooter:      getSetting("site_footer", defaultSettings["site_footer"]),
		SiteNotice:      getSetting("site_notice", defaultSettings["site_notice"]),
		SiteClosed:      getSetting("site_closed", defaultSettings["site_closed"]) == "true",
		ClosedMessage:   getSetting("closed_message", defaultSettings["closed_message"]),
		RegisterClosed:  getSetting("register_closed", defaultSettings["register_closed"]) == "true",
		MailHost:        getSetting("mail_host", defaultSettings["mail_host"]),
		MailPort:        func() int {
			p, err := strconv.Atoi(getSetting("mail_port", defaultSettings["mail_port"]))
			if err != nil {
				p, _ = strconv.Atoi(defaultSettings["mail_port"])
			}
			return p
		}(),
		MailUsername:   getSetting("mail_username", defaultSettings["mail_username"]),
		MailPassword:   getSetting("mail_password", defaultSettings["mail_password"]),
		MailFromName:   getSetting("mail_from_name", defaultSettings["mail_from_name"]),
		MailFromEmail:  getSetting("mail_from_email", defaultSettings["mail_from_email"]),
	}
	
	return utils.Success(c, "更新网站设置成功", settings)
}

// GetPublicSettings 获取公开的系统设置
// @Summary 获取公开的系统设置
// @Description 获取前端需要的公开系统设置，如是否开启注册、是否需要邀请码等
// @Tags 系统设置
// @Accept json
// @Produce json
// @Success 200 {object} dto.StandardResponse{data=map[string]string} "成功获取公开设置"
// @Failure 500 {object} dto.StandardResponse "服务器内部错误"
// @Router /admin/settings/public [get]
func GetPublicSettings(c *fiber.Ctx) error {
	// 直接从数据库获取设置，不使用utils.GetSetting
	// 这样可以避免认证检查
	publicSettingKeys := []string{
		"register_closed",
		"invitation_required",
		"site_name",
		"site_notice",
		"site_closed",
		"closed_message",
		"email_verification_required",
		"site_description",
		"site_footer",
		"site_logo",
	}
	
	// 定义默认值
	defaultValues := map[string]string{
		"register_closed":            "false",
		"invitation_required":        "true",
		"site_name":                  "Go Fiber API",
		"site_notice":                "",
		"site_closed":                "false",
		"closed_message":             "网站维护中，请稍后再试",
		"email_verification_required": "false",
		"site_description":           "欢迎使用XSS测试平台，安全测试的最佳选择",
		"site_footer":                "© 2020 - " + time.Now().Format("2006") + " XSS测试平台",
		"site_logo":                  "",
	}
	
	// 结果映射
	publicSettings := make(map[string]string)
	
	// 从数据库获取设置
	for _, key := range publicSettingKeys {
		// 使用getSetting函数，它不依赖认证
		publicSettings[key] = getSetting(key, defaultValues[key])
	}
	
	return utils.Success(c, "获取公开设置成功", publicSettings)
} 