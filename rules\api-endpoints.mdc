---
description: 
globs: 
alwaysApply: false
---
# API接口列表

本文档概述了 [接口文档.md](mdc:接口文档.md) 中定义的API接口。

## 接口分类

- **Web接口**：面向前端的接口，包括网关接口。
- **API接口**：通用的API接口。
- **Admin接口**：后台管理系统接口，包含多个模块。

## Web接口

### Gateway接口
- `/logs` (GET): 查看日志
- `/submit` (GET/POST): 提交数据
- `/keepsession` (GET): 保持会话
- `/download/{hash}` (GET): 下载文件
- `/{unique_key}/{rand_key}.jpg` (GET): 获取唯一图片
- `/{unique_key}` (GET): 获取唯一内容

## API接口

- `/api/User` (GET): 获取当前用户信息

## Admin接口

### 认证相关
- `/admin/auth/signup` (GET/POST): 注册
- `/admin/auth/forgot` (GET/POST): 忘记密码
- `/admin/captcha` (GET): 获取验证码
- `/admin/friendly-list` (GET): 获取友情链接列表

### 项目管理 (ProjectController)
- `/admin/project/my` (GET/POST): 列表/创建
- `/admin/project/my/{id}` (GET/PUT/PATCH/DELETE): 详情/更新/删除
- `/admin/project/my/create` (GET): 创建页面
- `/admin/project/my/{id}/edit` (GET): 编辑页面
- `/admin/project/viewcode/{projectid}` (GET): 查看代码
- `/admin/project/my/preview` (GET): 预览

### 项目管理 (ProjectsContentController)
- `/admin/project/my/{userid}/{projectid}` (GET/DELETE): 详情/删除

### 公告管理 (ArticleController)
- `/admin/article/index` (GET/POST): 列表/创建
- `/admin/article/index/{id}/edit` (GET): 编辑页面
- `/admin/article/index/{id}` (PUT/PATCH): 更新


### 模块管理 (ModuleController)
- CRUD操作 `/admin/module/index`

### 过滤域管理 (DomainController)
- CRUD操作 `/admin/domain/index`

### 项目内容管理 (ProjectsContentController)
- CRUD操作 `/admin/projectscontent/index`

### 邀请码管理 (InvitationController)
- CRUD操作 `/admin/invitation/index`

### 友情链接管理 (FriendlyController)
- CRUD操作 `/admin/friendly/index`

### 网站配置 (SettingsController)
- `/admin/settings` (GET): 配置页面

## 注意事项

- 本规则基于 [接口文档.md](mdc:接口文档.md) 文件。
- 实现时需注意路由参数、请求方法和控制器映射。

