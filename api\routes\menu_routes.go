package routes

import (
	"go-fiber-api/api/handlers"
	"go-fiber-api/api/middleware"

	"github.com/gofiber/fiber/v2"
)

// SetupMenuRoutes 设置菜单相关路由
func SetupMenuRoutes(api fiber.Router) {
	// 菜单管理路由组
	menuGroup := api.Group("/menu")

	// 添加认证中间件
	menuGroup.Use(middleware.AuthMiddleware())

	// 获取当前用户菜单树
	menuGroup.Get("/tree", handlers.GetUserMenuTree)
	
	// 菜单CRUD - 需要对应权限
	menuGroup.Get("/index", middleware.RequirePermission("menu.view"), handlers.GetMenus)
	menuGroup.Post("/index", middleware.RequirePermission("menu.create"), handlers.CreateMenu)
	menuGroup.Get("/index/:id", middleware.RequirePermission("menu.view"), handlers.GetMenuDetail)
	menuGroup.Put("/index/:id", middleware.RequirePermission("menu.edit"), handlers.UpdateMenu)
	menuGroup.Delete("/index/:id", middleware.RequirePermission("menu.delete"), handlers.DeleteMenu)
} 