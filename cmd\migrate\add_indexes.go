package main

import (
	"fmt"
	"go-fiber-api/config"
	"go-fiber-api/database"
	"log"
	"os"

	"github.com/joho/godotenv"
)

func main() {
	// 加载环境变量
	if err := godotenv.Load(); err != nil {
		log.Println("警告: 找不到.env文件")
	}

	// 初始化数据库连接
	if err := database.ConnDatabase(); err != nil {
		log.Fatalf("数据库连接失败: %v", err)
	}

	db := database.GetDB()
	
	log.Println("开始创建性能优化索引...")

	// 定义索引创建语句
	indexes := []struct {
		name  string
		query string
	}{
		// admin_menu 表索引
		{"admin_menu parent_id", "CREATE INDEX IF NOT EXISTS idx_admin_menu_parent_id ON admin_menu(parent_id)"},
		{"admin_menu parent_order", "CREATE INDEX IF NOT EXISTS idx_admin_menu_parent_order ON admin_menu(parent_id, `order`)"},
		{"admin_menu title", "CREATE INDEX IF NOT EXISTS idx_admin_menu_title ON admin_menu(title)"},

		// projects 表索引
		{"projects user_id", "CREATE INDEX IF NOT EXISTS idx_projects_user_id ON projects(user_id)"},
		{"projects state", "CREATE INDEX IF NOT EXISTS idx_projects_state ON projects(state)"},
		{"projects title", "CREATE INDEX IF NOT EXISTS idx_projects_title ON projects(title)"},
		{"projects unique_key", "CREATE INDEX IF NOT EXISTS idx_projects_unique_key ON projects(unique_key)"},
		{"projects user_state", "CREATE INDEX IF NOT EXISTS idx_projects_user_state ON projects(user_id, state)"},
		{"projects created_at", "CREATE INDEX IF NOT EXISTS idx_projects_created_at ON projects(created_at)"},
		{"projects deleted_at", "CREATE INDEX IF NOT EXISTS idx_projects_deleted_at ON projects(deleted_at)"},

		// projects_content 表索引
		{"projects_content project_id", "CREATE INDEX IF NOT EXISTS idx_projects_content_project_id ON projects_content(project_id)"},
		{"projects_content user_id", "CREATE INDEX IF NOT EXISTS idx_projects_content_user_id ON projects_content(user_id)"},
		{"projects_content project_user", "CREATE INDEX IF NOT EXISTS idx_projects_content_project_user ON projects_content(project_id, user_id)"},
		{"projects_content deleted_at", "CREATE INDEX IF NOT EXISTS idx_projects_content_deleted_at ON projects_content(deleted_at)"},

		// admin_role_menu 表索引
		{"admin_role_menu role_id", "CREATE INDEX IF NOT EXISTS idx_admin_role_menu_role_id ON admin_role_menu(role_id)"},
		{"admin_role_menu menu_id", "CREATE INDEX IF NOT EXISTS idx_admin_role_menu_menu_id ON admin_role_menu(menu_id)"},

		// admin_role_users 表索引
		{"admin_role_users user_id", "CREATE INDEX IF NOT EXISTS idx_admin_role_users_user_id ON admin_role_users(admin_user_id)"},
		{"admin_role_users role_id", "CREATE INDEX IF NOT EXISTS idx_admin_role_users_role_id ON admin_role_users(role_id)"},

		// admin_roles 表索引
		{"admin_roles name", "CREATE INDEX IF NOT EXISTS idx_admin_roles_name ON admin_roles(name)"},
		{"admin_roles all_menu_access", "CREATE INDEX IF NOT EXISTS idx_admin_roles_all_menu_access ON admin_roles(all_menu_access)"},

		// admin_users 表索引
		{"admin_users username", "CREATE INDEX IF NOT EXISTS idx_admin_users_username ON admin_users(username)"},
		{"admin_users email", "CREATE INDEX IF NOT EXISTS idx_admin_users_email ON admin_users(email)"},
		{"admin_users status", "CREATE INDEX IF NOT EXISTS idx_admin_users_status ON admin_users(status)"},
	}

	// 执行索引创建
	successCount := 0
	failCount := 0

	for _, idx := range indexes {
		log.Printf("创建索引: %s", idx.name)
		if err := db.Exec(idx.query).Error; err != nil {
			log.Printf("❌ 创建索引失败 [%s]: %v", idx.name, err)
			failCount++
		} else {
			log.Printf("✅ 创建索引成功 [%s]", idx.name)
			successCount++
		}
	}

	// 检查权限表是否存在并创建相应索引
	var tableExists int64
	db.Raw("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'admin_permissions'").Scan(&tableExists)
	
	if tableExists > 0 {
		permissionIndexes := []struct {
			name  string
			query string
		}{
			{"admin_permissions parent_id", "CREATE INDEX IF NOT EXISTS idx_admin_permissions_parent_id ON admin_permissions(parent_id)"},
			{"admin_permissions parent_order", "CREATE INDEX IF NOT EXISTS idx_admin_permissions_parent_order ON admin_permissions(parent_id, `order`)"},
			{"admin_permissions slug", "CREATE INDEX IF NOT EXISTS idx_admin_permissions_slug ON admin_permissions(slug)"},
		}

		for _, idx := range permissionIndexes {
			log.Printf("创建权限表索引: %s", idx.name)
			if err := db.Exec(idx.query).Error; err != nil {
				log.Printf("❌ 创建权限表索引失败 [%s]: %v", idx.name, err)
				failCount++
			} else {
				log.Printf("✅ 创建权限表索引成功 [%s]", idx.name)
				successCount++
			}
		}
	}

	// 输出结果统计
	log.Println("\n=== 索引创建完成 ===")
	log.Printf("成功: %d 个", successCount)
	log.Printf("失败: %d 个", failCount)
	log.Printf("总计: %d 个", successCount+failCount)

	if failCount > 0 {
		log.Println("\n⚠️  部分索引创建失败，请检查上述错误信息")
		os.Exit(1)
	} else {
		log.Println("\n🎉 所有索引创建成功！")
	}
}
