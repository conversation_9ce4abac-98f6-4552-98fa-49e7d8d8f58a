package handlers

import (
	"crypto/rand"
	"encoding/base32"
	"fmt"
	"go-fiber-api/api/dto"
	"go-fiber-api/database"
	"go-fiber-api/models"
	"go-fiber-api/utils"
	"log"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/pquerna/otp/totp"
)

// GenerateTwoFactorSecret 生成 2FA 密钥和二维码
// @Summary 生成双因素认证密钥
// @Description 为当前用户生成 TOTP 密钥和二维码，用于设置 Google Authenticator
// @Tags Two Factor Authentication
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Success 200 {object} dto.StandardResponse{data=dto.TwoFactorSetupResponse} "成功响应"
// @Failure 401 {object} dto.StandardResponse "未登录"
// @Failure 500 {object} dto.StandardResponse "生成密钥失败"
// @Router /admin/auth/2fa/generate [post]
func GenerateTwoFactorSecret(c *fiber.Ctx) error {
	// 获取当前用户ID
	userID := utils.GetUserIDFromContext(c)
	if userID == 0 {
		return utils.Unauthorized(c, "未登录或登录已过期", nil)
	}

	// 查找用户
	var user models.AdminUser
	if err := database.DB.First(&user, userID).Error; err != nil {
		return utils.NotFound(c, "用户不存在")
	}

	// 生成随机密钥
	secret := make([]byte, 20)
	_, err := rand.Read(secret)
	if err != nil {
		log.Printf("生成随机密钥失败: %v", err)
		return utils.ServerError(c, "生成密钥失败", err)
	}

	// 将密钥编码为 Base32
	secretKey := base32.StdEncoding.EncodeToString(secret)

	// 生成 TOTP 密钥
	key, err := totp.Generate(totp.GenerateOpts{
		Issuer:      "XSS测试平台",
		AccountName: user.Username,
		Secret:      secret,
	})
	if err != nil {
		log.Printf("生成 TOTP 密钥失败: %v", err)
		return utils.ServerError(c, "生成 TOTP 密钥失败", err)
	}

	// 临时存储密钥到 Redis（10分钟过期）
	tempKey := fmt.Sprintf("2fa_setup:%d", userID)
	if err := utils.StoreTemporaryData(tempKey, secretKey, 10*time.Minute); err != nil {
		log.Printf("存储临时密钥失败: %v", err)
		return utils.ServerError(c, "存储临时密钥失败", err)
	}

	return utils.Success(c, "2FA 密钥生成成功", dto.TwoFactorSetupResponse{
		Secret:    secretKey,
		QRCodeURL: key.URL(),
	})
}

// VerifyTwoFactorSetup 验证并启用 2FA
// @Summary 验证并启用双因素认证
// @Description 验证用户输入的 TOTP 代码，如果正确则启用双因素认证
// @Tags Two Factor Authentication
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param setup body dto.TwoFactorVerifyDTO true "验证数据"
// @Success 200 {object} dto.StandardResponse "启用成功"
// @Failure 400 {object} dto.StandardResponse "验证码错误"
// @Failure 401 {object} dto.StandardResponse "未登录"
// @Failure 500 {object} dto.StandardResponse "服务器错误"
// @Router /admin/auth/2fa/verify [post]
func VerifyTwoFactorSetup(c *fiber.Ctx) error {
	// 获取当前用户ID
	userID := utils.GetUserIDFromContext(c)
	if userID == 0 {
		return utils.Unauthorized(c, "未登录或登录已过期", nil)
	}

	// 解析请求数据
	verifyDTO := new(dto.TwoFactorVerifyDTO)
	if err := c.BodyParser(verifyDTO); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}

	// 验证输入
	if verifyDTO.Code == "" {
		return utils.BadRequest(c, "验证码不能为空", nil)
	}

	// 从 Redis 获取临时密钥
	tempKey := fmt.Sprintf("2fa_setup:%d", userID)
	secretKey, found := utils.RetrieveTemporaryData(tempKey)
	if !found {
		return utils.BadRequest(c, "密钥已过期，请重新生成", nil)
	}

	// 验证 TOTP 代码
	valid := totp.Validate(verifyDTO.Code, secretKey)
	if !valid {
		return utils.BadRequest(c, "验证码错误，请重试", nil)
	}

	// 查找用户
	var user models.AdminUser
	if err := database.DB.First(&user, userID).Error; err != nil {
		return utils.NotFound(c, "用户不存在")
	}

	// 启用 2FA
	now := time.Now()
	user.TwoFactorEnabled = true
	user.TwoFactorSecret = secretKey
	user.TwoFactorSetupAt = &now

	if err := database.DB.Save(&user).Error; err != nil {
		log.Printf("保存用户 2FA 设置失败: %v", err)
		return utils.ServerError(c, "启用双因素认证失败", err)
	}

	// 清除临时密钥
	utils.ClearTemporaryData(tempKey)

	log.Printf("User '%s' (ID=%d) enabled 2FA successfully", user.Username, user.ID)

	return utils.Success(c, "双因素认证启用成功", nil)
}

// DisableTwoFactor 禁用 2FA
// @Summary 禁用双因素认证
// @Description 禁用当前用户的双因素认证
// @Tags Two Factor Authentication
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param disable body dto.TwoFactorDisableDTO true "禁用数据"
// @Success 200 {object} dto.StandardResponse "禁用成功"
// @Failure 400 {object} dto.StandardResponse "验证码错误"
// @Failure 401 {object} dto.StandardResponse "未登录"
// @Failure 500 {object} dto.StandardResponse "服务器错误"
// @Router /admin/auth/2fa/disable [post]
func DisableTwoFactor(c *fiber.Ctx) error {
	// 获取当前用户ID
	userID := utils.GetUserIDFromContext(c)
	if userID == 0 {
		return utils.Unauthorized(c, "未登录或登录已过期", nil)
	}

	// 解析请求数据
	disableDTO := new(dto.TwoFactorDisableDTO)
	if err := c.BodyParser(disableDTO); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}

	// 验证输入
	if disableDTO.Code == "" {
		return utils.BadRequest(c, "验证码不能为空", nil)
	}

	// 查找用户
	var user models.AdminUser
	if err := database.DB.First(&user, userID).Error; err != nil {
		return utils.NotFound(c, "用户不存在")
	}

	// 检查用户是否启用了 2FA
	if !user.TwoFactorEnabled {
		return utils.BadRequest(c, "双因素认证未启用", nil)
	}

	// 验证 TOTP 代码
	valid := totp.Validate(disableDTO.Code, user.TwoFactorSecret)
	if !valid {
		return utils.BadRequest(c, "验证码错误，请重试", nil)
	}

	// 禁用 2FA
	user.TwoFactorEnabled = false
	user.TwoFactorSecret = ""
	user.TwoFactorSetupAt = nil

	if err := database.DB.Save(&user).Error; err != nil {
		log.Printf("保存用户 2FA 设置失败: %v", err)
		return utils.ServerError(c, "禁用双因素认证失败", err)
	}

	log.Printf("User '%s' (ID=%d) disabled 2FA", user.Username, user.ID)

	return utils.Success(c, "双因素认证已禁用", nil)
}

// GetTwoFactorStatus 获取 2FA 状态
// @Summary 获取双因素认证状态
// @Description 获取当前用户的双因素认证状态
// @Tags Two Factor Authentication
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Success 200 {object} dto.StandardResponse{data=dto.TwoFactorStatusResponse} "成功响应"
// @Failure 401 {object} dto.StandardResponse "未登录"
// @Router /admin/auth/2fa/status [get]
func GetTwoFactorStatus(c *fiber.Ctx) error {
	// 获取当前用户ID
	userID := utils.GetUserIDFromContext(c)
	if userID == 0 {
		return utils.Unauthorized(c, "未登录或登录已过期", nil)
	}

	// 查找用户
	var user models.AdminUser
	if err := database.DB.First(&user, userID).Error; err != nil {
		return utils.NotFound(c, "用户不存在")
	}

	return utils.Success(c, "获取 2FA 状态成功", dto.TwoFactorStatusResponse{
		Enabled:   user.TwoFactorEnabled,
		SetupAt:   user.TwoFactorSetupAt,
		IsRequired: !user.TwoFactorEnabled && user.TwoFactorSetupAt == nil && user.LastedAt == nil,
	})
}
