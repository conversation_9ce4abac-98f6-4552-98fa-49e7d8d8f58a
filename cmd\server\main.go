package main

import (
	"fmt"
	"log"
	"os"

	"go-fiber-api/api/routes"
	"go-fiber-api/config"
	"go-fiber-api/database"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/gofiber/fiber/v2/middleware/recover"
	"github.com/joho/godotenv"

	swagger "github.com/arsmn/fiber-swagger/v2"
)

// @title Go Fiber API
// @version 1.0
// @description 这是使用Go Fiber构建的API服务.
// @termsOfService http://swagger.io/terms/
// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>
// @license.name Apache 2.0
// @license.url http://www.apache.org/licenses/LICENSE-2.0.html
// @host localhost:3000
// @BasePath /api
// @schemes http https

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description 请输入 'Bearer {token}' - 注意Bearer和token之间有一个空格
func main() {
	// 加载环境变量
	if err := godotenv.Load(); err != nil {
		log.Println("警告: 找不到.env文件")
	}

	// 初始化配置
	appConfig := config.LoadConfig()

	// 确保uploads目录存在
	if err := os.MkdirAll("uploads", 0755); err != nil {
		log.Printf("警告: 创建uploads目录失败: %v", err)
	}

	// 设置数据库连接
	if err := database.ConnDatabase(); err != nil {
		log.Fatalf("数据库连接失败: %v", err)
	}

	// 初始化Redis连接
	if err := database.InitRedis(); err != nil {
		log.Fatalf("Redis连接失败: %v", err)
	}

	// 启动Redis通知订阅服务
	go func() {
		log.Println("启动Redis通知订阅服务...")
		pubsub := database.Rdb.Subscribe(database.RedisCtx, "notifications")
		defer pubsub.Close()
		
		// 处理通知消息
		for msg := range pubsub.Channel() {
			log.Printf("收到通知: %s", msg.Payload)
			// 广播通知给所有WebSocket客户端
			routes.BroadcastToWebSocketClients([]byte(msg.Payload))
		}
	}()

	// 初始化Fiber应用
	app := fiber.New(fiber.Config{
		AppName: appConfig.AppName,
	})

	// 基础中间件
	app.Use(logger.New())
	app.Use(recover.New())
	app.Use(cors.New(cors.Config{
		AllowOrigins: "*",
		AllowMethods: "GET,POST,PUT,DELETE,OPTIONS",
	}))

	// 设置静态文件服务
	app.Static("/static", "./public")
	// 特别为uploads目录设置静态服务
	app.Static("/static/uploads", "./uploads")

	// 设置Swagger路由
	app.Get("/swagger/*", swagger.HandlerDefault)

	// 设置API路由 (确保在Swagger路由之后)
	routes.SetupRoutes(app)

	// 打印调试信息
	fmt.Println("=== 应用配置 ===")
	fmt.Printf("数据库配置: %+v\n", appConfig.DBConfig)
	fmt.Printf("Redis配置: %+v\n", appConfig.RedisConfig)
	fmt.Printf("应用端口: %s\n", appConfig.Port)
	fmt.Println("=== 中间件 ===")
	fmt.Println("已启用: logger, recover, cors")
	fmt.Println("操作日志中间件已应用于路由组: /api/admin")
	
	// 获取端口或使用默认值
	port := os.Getenv("PORT")
	if port == "" {
		port = appConfig.Port
	}

	// 启动服务器
	log.Printf("服务器启动在端口 %s\n", port)
	if err := app.Listen(":" + port); err != nil {
		log.Fatalf("服务器启动失败: %v", err)
	}
}
