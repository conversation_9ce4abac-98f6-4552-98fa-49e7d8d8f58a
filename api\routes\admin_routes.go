package routes

import (
	"go-fiber-api/api/handlers"
	"go-fiber-api/api/middleware"
	"go-fiber-api/utils"

	"github.com/gofiber/fiber/v2"
)

// SetupAdminRoutes 设置管理员相关路由
func SetupAdminRoutes(router fiber.Router) {
	// 注意：不要在这里创建 /admin 组，因为会与认证路由冲突
	// 直接在传入的 router 上添加需要认证的路由

	// 管理员用户路由 - 使用具体路径避免与 /admin/auth 冲突
	users := router.Group("/admin/users")
	users.Use(utils.RequireAuthentication) // 只对用户管理路由应用认证
	users.Use(middleware.OperationLogMiddleware()) // 操作日志中间件

	users.Get("/", middleware.RequirePermission("users.view"), handlers.GetAdminUsers)
	users.Get("/:id", middleware.RequirePermission("users.view"), handlers.GetAdminUser)
	users.Post("/", middleware.RequirePermission("users.create"), handlers.CreateAdminUser)
	users.Put("/:id", middleware.RequirePermission("users.edit"), handlers.UpdateAdminUser)
	users.Delete("/:id", middleware.RequirePermission("users.delete"), handlers.DeleteAdminUser)
}
