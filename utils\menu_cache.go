package utils

import (
	"encoding/json"
	"fmt"
	"go-fiber-api/database"
	"go-fiber-api/dto"
	"go-fiber-api/models"
	"log"
	"sync"
	"time"
)

const (
	menuCacheKey        = "menu_cache:all"
	menuTreeCacheKey    = "menu_cache:tree"
	menuCacheExpiration = 30 * time.Minute // 缓存30分钟
)

var (
	menuCacheMutex sync.RWMutex
	lastMenuUpdate time.Time
)

// MenuCacheService 菜单缓存服务
type MenuCacheService struct{}

// GetAllMenusFromCache 从缓存获取所有菜单
func (s *MenuCacheService) GetAllMenusFromCache() ([]models.Menu, error) {
	// 尝试从Redis获取
	cachedData, err := database.Rdb.Get(database.RedisCtx, menuCacheKey).Result()
	if err == nil {
		var menus []models.Menu
		if err := json.Unmarshal([]byte(cachedData), &menus); err == nil {
			log.Printf("菜单缓存命中: 从Redis获取到 %d 个菜单", len(menus))
			return menus, nil
		}
	}

	// 缓存未命中，从数据库获取
	log.Println("菜单缓存未命中，从数据库获取")
	var menus []models.Menu
	if err := database.DB.Order("parent_id ASC, `order` ASC").Find(&menus).Error; err != nil {
		return nil, fmt.Errorf("从数据库获取菜单失败: %w", err)
	}

	// 存储到Redis缓存
	if data, err := json.Marshal(menus); err == nil {
		database.Rdb.Set(database.RedisCtx, menuCacheKey, data, menuCacheExpiration)
		log.Printf("菜单数据已缓存到Redis: %d 个菜单", len(menus))
	}

	return menus, nil
}

// GetMenuTreeFromCache 从缓存获取菜单树
func (s *MenuCacheService) GetMenuTreeFromCache() ([]dto.MenuResponse, error) {
	// 尝试从Redis获取菜单树
	cachedData, err := database.Rdb.Get(database.RedisCtx, menuTreeCacheKey).Result()
	if err == nil {
		var menuTree []dto.MenuResponse
		if err := json.Unmarshal([]byte(cachedData), &menuTree); err == nil {
			log.Printf("菜单树缓存命中: 从Redis获取到 %d 个顶级菜单", len(menuTree))
			return menuTree, nil
		}
	}

	// 缓存未命中，构建菜单树
	log.Println("菜单树缓存未命中，重新构建")
	menus, err := s.GetAllMenusFromCache()
	if err != nil {
		return nil, err
	}

	// 构建菜单树
	menuTree := s.buildMenuTreeOptimized(menus, 0)

	// 存储到Redis缓存
	if data, err := json.Marshal(menuTree); err == nil {
		database.Rdb.Set(database.RedisCtx, menuTreeCacheKey, data, menuCacheExpiration)
		log.Printf("菜单树已缓存到Redis: %d 个顶级菜单", len(menuTree))
	}

	return menuTree, nil
}

// buildMenuTreeOptimized 优化的菜单树构建方法
func (s *MenuCacheService) buildMenuTreeOptimized(menus []models.Menu, parentID uint64) []dto.MenuResponse {
	// 创建菜单映射以提高查找效率
	menuMap := make(map[uint64][]models.Menu)
	for _, menu := range menus {
		menuMap[menu.ParentID] = append(menuMap[menu.ParentID], menu)
	}
	
	return s.buildMenuTreeRecursive(menuMap, parentID)
}

// buildMenuTreeRecursive 递归构建菜单树的内部方法
func (s *MenuCacheService) buildMenuTreeRecursive(menuMap map[uint64][]models.Menu, parentID uint64) []dto.MenuResponse {
	var tree []dto.MenuResponse
	
	children, exists := menuMap[parentID]
	if !exists {
		return tree
	}
	
	for _, menu := range children {
		node := dto.MenuResponse{
			ID:       menu.ID,
			ParentID: menu.ParentID,
			Order:    menu.Order,
			Title:    menu.Title,
			Icon:     menu.Icon,
			URI:      menu.URI,
		}
		
		// 递归查找子菜单
		subChildren := s.buildMenuTreeRecursive(menuMap, menu.ID)
		if len(subChildren) > 0 {
			node.Children = subChildren
		}
		
		tree = append(tree, node)
	}
	
	return tree
}

// InvalidateMenuCache 清除菜单缓存
func (s *MenuCacheService) InvalidateMenuCache() error {
	menuCacheMutex.Lock()
	defer menuCacheMutex.Unlock()
	
	// 清除Redis缓存
	err1 := database.Rdb.Del(database.RedisCtx, menuCacheKey).Err()
	err2 := database.Rdb.Del(database.RedisCtx, menuTreeCacheKey).Err()
	
	lastMenuUpdate = time.Now()
	log.Println("菜单缓存已清除")
	
	if err1 != nil {
		return fmt.Errorf("清除菜单缓存失败: %w", err1)
	}
	if err2 != nil {
		return fmt.Errorf("清除菜单树缓存失败: %w", err2)
	}
	
	return nil
}

// GetMenuCacheService 获取菜单缓存服务实例
func GetMenuCacheService() *MenuCacheService {
	return &MenuCacheService{}
}

// FilterMenusByPermission 根据权限过滤菜单
func (s *MenuCacheService) FilterMenusByPermission(menus []models.Menu, permissionMap map[uint64]bool) []models.Menu {
	var filteredMenus []models.Menu
	
	// 递归查找所有父级菜单ID，确保菜单结构完整
	parentMenuIDs := make(map[uint64]bool)
	
	// 递归函数，查找所有父菜单
	var findParentMenus func(menuID uint64)
	findParentMenus = func(menuID uint64) {
		for _, menu := range menus {
			if menu.ID == menuID {
				// 如果有父菜单且尚未处理过
				if menu.ParentID > 0 && !parentMenuIDs[menu.ParentID] && !permissionMap[menu.ParentID] {
					parentMenuIDs[menu.ParentID] = true
					// 递归处理父菜单的父菜单
					findParentMenus(menu.ParentID)
				}
				break
			}
		}
	}
	
	// 为每个用户有权限的菜单查找其父菜单
	for menuID := range permissionMap {
		findParentMenus(menuID)
	}
	
	// 合并用户有权限访问的菜单和必要的父菜单
	authorizedMenuIDs := make(map[uint64]bool)
	for menuID := range permissionMap {
		authorizedMenuIDs[menuID] = true
	}
	for menuID := range parentMenuIDs {
		authorizedMenuIDs[menuID] = true
	}
	
	// 创建授权菜单列表
	for _, menu := range menus {
		if authorizedMenuIDs[menu.ID] {
			filteredMenus = append(filteredMenus, menu)
		}
	}
	
	return filteredMenus
}

// BuildMenuTreeWithPermission 构建带权限检查的菜单树
func (s *MenuCacheService) BuildMenuTreeWithPermission(menus []models.Menu, parentID uint64, permissionMap map[uint64]bool) []dto.MenuResponse {
	var tree []dto.MenuResponse

	for _, menu := range menus {
		if menu.ParentID == parentID {
			// 创建菜单节点
			node := dto.MenuResponse{
				ID:       menu.ID,
				ParentID: menu.ParentID,
				Order:    menu.Order,
				Title:    menu.Title,
				Icon:     menu.Icon,
				URI:      menu.URI,
			}
			
			// 递归查找子菜单，只包含有权限的子菜单
			children := s.BuildMenuTreeWithPermission(menus, menu.ID, permissionMap)
			
			// 仅当当前菜单有权限访问或者有子菜单时才添加到树中
			if permissionMap[menu.ID] || len(children) > 0 {
				if len(children) > 0 {
					node.Children = children
				}
				tree = append(tree, node)
			}
		}
	}

	return tree
}
