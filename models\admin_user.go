package models

import (
	"time"
	"gorm.io/gorm"
)

// AdminUser 管理员用户模型
type AdminUser struct {
	ID              uint64         `json:"id" gorm:"primaryKey;type:bigint unsigned;comment:用户ID"`
	Username        string         `json:"username" gorm:"uniqueIndex:admin_users_username_unique;not null;size:120;comment:用户名"`
	Password        string         `json:"-" gorm:"not null;size:80;comment:密码"`
	Alias           string         `json:"alias" gorm:"not null;default:'';size:128;comment:随机生成字符"`
	Name            string         `json:"name" gorm:"not null;comment:姓名"`
	Email           string         `json:"email" gorm:"size:100;comment:邮箱"`
	EmailVerified   bool           `json:"email_verified" gorm:"default:false;comment:邮箱是否已验证"`
	Avatar          string         `json:"avatar" gorm:"size:255;comment:头像"`
	Referee         string         `json:"referee" gorm:"not null;comment:邀请人"`
	Notify          int8           `json:"notify" gorm:"default:1;type:tinyint;comment:1 接收/0 不接收"`
	RememberToken   string         `json:"-" gorm:"column:remember_token;size:100;comment:TOKEN"`
	LastedIPAddress string         `json:"lasted_ipaddress" gorm:"column:lasted_ipaddress;size:128;comment:最后登录IP"`
	LastedAt        *time.Time     `json:"lasted_at" gorm:"comment:最后登录时间"`
	State           int8           `json:"state" gorm:"default:1;type:tinyint(1);comment:启用"`
	// 2FA 相关字段
	TwoFactorEnabled bool           `json:"two_factor_enabled" gorm:"default:false;comment:是否启用双因素认证"`
	TwoFactorSecret  string         `json:"-" gorm:"size:255;comment:TOTP密钥"`
	TwoFactorSetupAt *time.Time     `json:"two_factor_setup_at" gorm:"comment:2FA设置时间"`
	IsAdmin          bool           `json:"is_admin" gorm:"-"` // 虚拟字段，不存储到数据库
	CreatedAt       time.Time      `json:"created_at" gorm:"comment:创建时间戳"`
	UpdatedAt       time.Time      `json:"updated_at" gorm:"comment:更新时间戳"`
	DeletedAt       gorm.DeletedAt `json:"-" gorm:"index;comment:删除时间"`

	Roles       []Role       `gorm:"many2many:admin_role_users;foreignKey:ID;joinForeignKey:UserID;References:ID;joinReferences:RoleID" json:"roles,omitempty"`
	Permissions []Permission `gorm:"many2many:admin_user_permissions;foreignKey:ID;joinForeignKey:UserID;References:ID;joinReferences:PermissionID" json:"permissions,omitempty"`
}

// TableName 指定表名
func (AdminUser) TableName() string {
	return "admin_users"
}

// BeforeCreate 创建前的钩子函数
func (u *AdminUser) BeforeCreate(tx *gorm.DB) error {
	// TODO: 可以在这里添加密码加密等逻辑
	return nil
}

// BeforeUpdate 更新前的钩子函数
func (u *AdminUser) BeforeUpdate(tx *gorm.DB) error {
	// TODO: 可以在这里添加密码更新逻辑
	return nil
}
