basePath: /api
definitions:
  dto.AdminUserCreateDTO:
    properties:
      avatar:
        type: string
      email:
        type: string
      name:
        type: string
      notify:
        type: integer
      password:
        maxLength: 80
        minLength: 6
        type: string
      referee:
        type: string
      state:
        enum:
        - 0
        - 1
        type: integer
      username:
        maxLength: 120
        minLength: 3
        type: string
    required:
    - name
    - password
    - username
    type: object
  dto.AdminUserResponse:
    properties:
      avatar:
        type: string
      created_at:
        type: string
      email:
        type: string
      id:
        type: integer
      lasted_at:
        type: string
      lasted_ipaddress:
        type: string
      name:
        type: string
      notify:
        type: integer
      referee:
        type: string
      state:
        type: integer
      updated_at:
        type: string
      username:
        type: string
    type: object
  dto.AdminUserUpdateDTO:
    properties:
      avatar:
        type: string
      email:
        type: string
      name:
        type: string
      notify:
        type: integer
      password:
        maxLength: 80
        minLength: 6
        type: string
      referee:
        type: string
      state:
        enum:
        - 0
        - 1
        type: integer
      username:
        maxLength: 120
        minLength: 3
        type: string
    type: object
  dto.ArticleListItem:
    properties:
      author:
        type: string
      created_at:
        type: string
      id:
        type: integer
      status:
        type: integer
      title:
        type: string
    type: object
  dto.ArticleRequest:
    properties:
      author:
        maxLength: 100
        type: string
      description:
        type: string
      status:
        enum:
        - 0
        - 1
        type: integer
      title:
        maxLength: 255
        minLength: 2
        type: string
    required:
    - description
    - title
    type: object
  dto.ArticleResponse:
    properties:
      author:
        type: string
      created_at:
        type: string
      created_by:
        type: integer
      description:
        type: string
      id:
        type: integer
      status:
        type: integer
      title:
        type: string
      updated_at:
        type: string
      updated_by:
        type: integer
    type: object
  dto.ArticleUpdateRequest:
    properties:
      author:
        maxLength: 100
        type: string
      description:
        type: string
      status:
        enum:
        - 0
        - 1
        type: integer
      title:
        maxLength: 255
        minLength: 2
        type: string
    type: object
  dto.AuthForgotPasswordDTO:
    properties:
      captcha:
        type: string
      captcha_key:
        type: string
      email:
        type: string
      username:
        type: string
    required:
    - captcha
    - captcha_key
    - email
    - username
    type: object
  dto.AuthLoginDTO:
    properties:
      captcha:
        type: string
      captcha_key:
        type: string
      password:
        type: string
      username:
        type: string
    required:
    - captcha
    - captcha_key
    - password
    - username
    type: object
  dto.AuthResetPasswordDTO:
    properties:
      new_password:
        maxLength: 80
        minLength: 6
        type: string
      token:
        type: string
    required:
    - new_password
    - token
    type: object
  dto.AuthResponse:
    properties:
      token:
        type: string
    type: object
  dto.AuthSignupDTO:
    properties:
      captcha:
        type: string
      captcha_key:
        type: string
      email:
        type: string
      name:
        type: string
      password:
        maxLength: 80
        minLength: 6
        type: string
      referee:
        description: Made optional for validation, logic handled in handler
        type: string
      username:
        maxLength: 120
        minLength: 3
        type: string
    required:
    - captcha
    - captcha_key
    - name
    - password
    - username
    type: object
  dto.BatchIDsDTO:
    properties:
      ids:
        description: ID列表
        items:
          type: integer
        minItems: 1
        type: array
    required:
    - ids
    type: object
  dto.CaptchaResponse:
    properties:
      captcha_key:
        description: 验证码唯一标识
        type: string
      image_data:
        description: Base64编码的图片数据 (e.g., data:image/png;base64,...)
        type: string
    type: object
  dto.DashboardDataResponse:
    properties:
      menu_count:
        description: 菜单总数
        type: integer
      permission_count:
        description: 权限总数
        type: integer
      project_count:
        description: 项目总数
        type: integer
      role_count:
        description: 角色总数
        type: integer
      today_project_count:
        description: 今日新增项目数
        type: integer
      today_user_count:
        description: 今日新增用户数
        type: integer
      user_count:
        description: 用户总数
        type: integer
    type: object
  dto.DomainListItem:
    properties:
      created_at:
        description: 创建时间
        type: string
      domain:
        description: 域名
        type: string
      id:
        description: 域名ID
        type: integer
      state:
        description: 状态
        type: integer
      type:
        description: 类型 10 过滤域名/20 切换使用的域名
        type: integer
    type: object
  dto.DomainRequest:
    properties:
      domain:
        description: 域名
        type: string
      state:
        description: 状态
        maximum: 1
        minimum: 0
        type: integer
      type:
        description: 类型 10 过滤域名/20 切换使用的域名
        enum:
        - 10
        - 20
        type: integer
    required:
    - domain
    - state
    - type
    type: object
  dto.DomainResponse:
    properties:
      created_at:
        description: 创建时间
        type: string
      domain:
        description: 域名
        type: string
      id:
        description: 域名ID
        type: integer
      state:
        description: 状态
        type: integer
      type:
        description: 类型 10 过滤域名/20 切换使用的域名
        type: integer
      updated_at:
        description: 更新时间
        type: string
    type: object
  dto.DomainUpdateRequest:
    properties:
      domain:
        description: 域名
        type: string
      state:
        description: 状态
        maximum: 1
        minimum: 0
        type: integer
      type:
        description: 类型 10 过滤域名/20 切换使用的域名
        enum:
        - 10
        - 20
        type: integer
    type: object
  dto.EmailRequest:
    properties:
      body:
        type: string
      is_html:
        type: boolean
      subject:
        type: string
      to:
        type: string
    required:
    - body
    - subject
    - to
    type: object
  dto.FailedJobCreateRequest:
    properties:
      connection:
        type: string
      exception:
        type: string
      payload:
        type: string
      queue:
        type: string
    required:
    - connection
    - exception
    - payload
    - queue
    type: object
  dto.FailedJobListItem:
    properties:
      connection:
        type: string
      exception:
        type: string
      failed_at:
        type: string
      id:
        type: integer
      queue:
        type: string
    type: object
  dto.FailedJobResponse:
    properties:
      connection:
        type: string
      exception:
        type: string
      failed_at:
        type: string
      id:
        type: integer
      payload:
        type: string
      queue:
        type: string
    type: object
  dto.FailedJobUpdateRequest:
    properties:
      connection:
        type: string
      exception:
        type: string
      payload:
        type: string
      queue:
        type: string
    type: object
  dto.FriendlyListItem:
    properties:
      created_at:
        description: 创建时间
        type: string
      href:
        description: 链接地址
        type: string
      id:
        description: 友情链接ID
        type: integer
      name:
        description: 名称
        type: string
      state:
        description: 状态
        type: integer
      thumb:
        description: 缩略图
        type: string
      type:
        description: 类型
        type: string
    type: object
  dto.FriendlyRequest:
    properties:
      href:
        description: 链接地址
        type: string
      name:
        description: 名称
        type: string
      state:
        description: 状态
        maximum: 1
        minimum: 0
        type: integer
      thumb:
        description: 缩略图
        type: string
      type:
        description: 类型
        type: string
    required:
    - href
    - name
    - type
    type: object
  dto.FriendlyResponse:
    properties:
      created_at:
        description: 创建时间
        type: string
      href:
        description: 链接地址
        type: string
      id:
        description: 友情链接ID
        type: integer
      name:
        description: 名称
        type: string
      state:
        description: 状态
        type: integer
      thumb:
        description: 缩略图
        type: string
      type:
        description: 类型
        type: string
      updated_at:
        description: 更新时间
        type: string
    type: object
  dto.FriendlyUpdateRequest:
    properties:
      href:
        description: 链接地址
        type: string
      name:
        description: 名称
        type: string
      state:
        description: 状态
        maximum: 1
        minimum: 0
        type: integer
      thumb:
        description: 缩略图
        type: string
      type:
        description: 类型
        type: string
    type: object
  dto.InvitationBatchRequest:
    properties:
      action:
        description: 操作类型
        enum:
        - enable
        - disable
        - delete
        type: string
      ids:
        description: 邀请码ID列表
        items:
          type: integer
        type: array
    required:
    - action
    - ids
    type: object
  dto.InvitationListItem:
    properties:
      code:
        description: 邀请码
        type: string
      created_at:
        description: 创建时间
        type: string
      id:
        description: 邀请码ID
        type: integer
      state:
        description: 状态
        type: integer
      used_at:
        description: 使用时间
        type: string
      user_id:
        description: 创建用户ID
        type: integer
    type: object
  dto.InvitationRequest:
    properties:
      code:
        description: 邀请码
        type: string
      count:
        description: 批量生成数量
        maximum: 100
        minimum: 1
        type: integer
      state:
        description: 状态
        maximum: 1
        minimum: 0
        type: integer
    required:
    - code
    type: object
  dto.InvitationResponse:
    properties:
      code:
        description: 邀请码
        type: string
      created_at:
        description: 创建时间
        type: string
      id:
        description: 邀请码ID
        type: integer
      state:
        description: 状态
        type: integer
      updated_at:
        description: 更新时间
        type: string
      used_at:
        description: 使用时间
        type: string
      user_id:
        description: 创建用户ID
        type: integer
    type: object
  dto.InvitationUpdateRequest:
    properties:
      code:
        description: 邀请码
        type: string
      state:
        description: 状态
        maximum: 1
        minimum: 0
        type: integer
    type: object
  dto.JobCreateRequest:
    properties:
      attempts:
        type: integer
      available_at:
        type: integer
      payload:
        type: string
      queue:
        type: string
    required:
    - attempts
    - available_at
    - payload
    - queue
    type: object
  dto.JobListItem:
    properties:
      attempts:
        type: integer
      available_at:
        type: integer
      created_at:
        type: integer
      id:
        type: integer
      queue:
        type: string
      reserved_at:
        type: integer
    type: object
  dto.JobResponse:
    properties:
      attempts:
        type: integer
      available_at:
        type: integer
      created_at:
        type: integer
      id:
        type: integer
      payload:
        type: string
      queue:
        type: string
      reserved_at:
        type: integer
    type: object
  dto.JobUpdateRequest:
    properties:
      attempts:
        type: integer
      available_at:
        type: integer
      payload:
        type: string
      queue:
        type: string
      reserved_at:
        type: integer
    type: object
  dto.MenuCreateDTO:
    properties:
      icon:
        type: string
      order:
        type: integer
      parent_id:
        type: integer
      permission_ids:
        items:
          type: integer
        type: array
      role_ids:
        items:
          type: integer
        type: array
      title:
        type: string
      uri:
        type: string
    required:
    - title
    type: object
  dto.MenuDetailResponse:
    properties:
      icon:
        type: string
      id:
        type: integer
      order:
        type: integer
      parent_id:
        type: integer
      role_ids:
        items:
          type: integer
        type: array
      title:
        type: string
      uri:
        type: string
    type: object
  dto.MenuResponse:
    properties:
      children:
        items:
          $ref: '#/definitions/dto.MenuResponse'
        type: array
      icon:
        type: string
      id:
        type: integer
      order:
        type: integer
      parent_id:
        type: integer
      title:
        type: string
      uri:
        type: string
    type: object
  dto.MenuUpdateDTO:
    properties:
      icon:
        type: string
      order:
        type: integer
      parent_id:
        type: integer
      role_ids:
        items:
          type: integer
        type: array
      title:
        type: string
      uri:
        type: string
    required:
    - title
    type: object
  dto.ModuleListItem:
    properties:
      created_at:
        description: 创建时间
        type: string
      description:
        description: 模块描述
        type: string
      id:
        description: 模块ID
        type: integer
      is_share:
        description: 共享状态
        type: integer
      level:
        description: 安全等级
        type: integer
      state:
        description: 状态
        type: integer
      title:
        description: 模块名称
        type: string
    type: object
  dto.ModuleRequest:
    properties:
      code:
        description: HTML代码
        type: string
      description:
        description: 模块描述
        type: string
      is_share:
        description: 共享状态
        maximum: 1
        minimum: 0
        type: integer
      keys:
        description: 关键字
        type: string
      level:
        description: 安全等级
        maximum: 9
        minimum: 0
        type: integer
      setkeys:
        description: 配置参数
        type: string
      state:
        description: 状态
        maximum: 1
        minimum: 0
        type: integer
      title:
        description: 模块名称
        type: string
    required:
    - code
    - title
    type: object
  dto.ModuleResponse:
    properties:
      code:
        description: HTML代码
        type: string
      created_at:
        description: 创建时间
        type: string
      description:
        description: 模块描述
        type: string
      id:
        description: 模块ID
        type: integer
      is_share:
        description: 共享状态
        type: integer
      keys:
        description: 关键字
        type: string
      level:
        description: 安全等级
        type: integer
      setkeys:
        description: 配置参数
        type: string
      state:
        description: 状态
        type: integer
      title:
        description: 模块名称
        type: string
      updated_at:
        description: 更新时间
        type: string
      user_id:
        description: 创建人ID
        type: integer
    type: object
  dto.ModuleUpdateRequest:
    properties:
      code:
        description: HTML代码
        type: string
      description:
        description: 模块描述
        type: string
      is_share:
        description: 共享状态
        maximum: 1
        minimum: 0
        type: integer
      keys:
        description: 关键字
        type: string
      level:
        description: 安全等级
        maximum: 9
        minimum: 0
        type: integer
      setkeys:
        description: 配置参数
        type: string
      state:
        description: 状态
        maximum: 1
        minimum: 0
        type: integer
      title:
        description: 模块名称
        type: string
    type: object
  dto.OperationLogResponse:
    properties:
      created_at:
        description: 创建时间
        type: string
      id:
        description: 日志ID
        type: integer
      input:
        description: 输入数据
        type: string
      ip:
        description: IP地址
        type: string
      method:
        description: HTTP方法
        type: string
      path:
        description: 操作路径
        type: string
      user_id:
        description: 用户ID
        type: integer
      username:
        description: 用户名
        type: string
    type: object
  dto.PaginatedResponse:
    properties:
      items:
        description: 当前页数据项
      page:
        description: 当前页码
        type: integer
      page_size:
        description: 每页大小
        type: integer
      total:
        description: 总记录数
        type: integer
      total_pages:
        description: 总页数
        type: integer
    type: object
  dto.PermissionCreateDTO:
    properties:
      http_method:
        type: string
      http_path:
        type: string
      menu_ids:
        items:
          type: integer
        type: array
      name:
        maxLength: 50
        type: string
      order:
        default: 0
        type: integer
      parent_id:
        default: 0
        type: integer
      role_ids:
        items:
          type: integer
        type: array
      slug:
        maxLength: 50
        type: string
    required:
    - name
    - slug
    type: object
  dto.PermissionDetailResponse:
    properties:
      http_method:
        type: string
      http_path:
        type: string
      id:
        type: integer
      menu_ids:
        items:
          type: integer
        type: array
      name:
        type: string
      order:
        type: integer
      parent_id:
        type: integer
      role_ids:
        items:
          type: integer
        type: array
      slug:
        type: string
    type: object
  dto.PermissionResponse:
    properties:
      children:
        items:
          $ref: '#/definitions/dto.PermissionResponse'
        type: array
      http_method:
        type: string
      http_path:
        type: string
      id:
        type: integer
      name:
        type: string
      order:
        type: integer
      parent_id:
        type: integer
      slug:
        type: string
    type: object
  dto.PermissionUpdateDTO:
    properties:
      http_method:
        type: string
      http_path:
        type: string
      menu_ids:
        items:
          type: integer
        type: array
      name:
        maxLength: 50
        type: string
      order:
        type: integer
      parent_id:
        type: integer
      role_ids:
        items:
          type: integer
        type: array
      slug:
        maxLength: 50
        type: string
    required:
    - name
    - slug
    type: object
  dto.ProfileResponse:
    properties:
      avatar:
        type: string
      email:
        type: string
      id:
        type: integer
      name:
        type: string
      permissions:
        description: 用户权限列表
        items:
          type: string
        type: array
      role_slugs:
        description: 用户角色标识列表
        items:
          type: string
        type: array
      roles:
        items:
          $ref: '#/definitions/dto.RoleItem'
        type: array
      username:
        type: string
    type: object
  dto.ProjectContentListItem:
    properties:
      cid:
        type: integer
      content:
        type: string
      created_at:
        type: string
      domain:
        type: string
      hash:
        type: string
      project_id:
        type: integer
      screenshot:
        description: 屏幕截图URL路径
        type: string
      server:
        type: string
      source_file:
        description: 源码文件URL路径
        type: string
      state:
        type: integer
    type: object
  dto.ProjectContentRequest:
    properties:
      content:
        type: string
      domain:
        maxLength: 255
        type: string
      hash:
        maxLength: 32
        type: string
      project_id:
        type: integer
      screenshot:
        maxLength: 500
        type: string
      server:
        type: string
      source_file:
        type: string
      state:
        enum:
        - 0
        - 1
        type: integer
    required:
    - content
    - domain
    - hash
    - project_id
    - server
    - state
    type: object
  dto.ProjectContentResponse:
    properties:
      cid:
        type: integer
      content:
        type: string
      created_at:
        type: string
      domain:
        type: string
      hash:
        type: string
      project_id:
        type: integer
      screenshot:
        description: 屏幕截图URL路径
        type: string
      server:
        type: string
      source_file:
        description: 源码文件URL路径
        type: string
      state:
        type: integer
      updated_at:
        type: string
      user_id:
        type: integer
    type: object
  dto.ProjectContentUpdateRequest:
    properties:
      content:
        type: string
      domain:
        maxLength: 255
        type: string
      hash:
        maxLength: 32
        type: string
      server:
        type: string
      state:
        enum:
        - 0
        - 1
        type: integer
    type: object
  dto.ProjectListItem:
    properties:
      content_count:
        description: 项目内容记录数
        type: integer
      created_at:
        type: string
      description:
        type: string
      id:
        type: integer
      state:
        type: integer
      title:
        type: string
      unique_key:
        type: string
    type: object
  dto.ProjectRequest:
    properties:
      code:
        type: string
      description:
        maxLength: 255
        type: string
      module_ext_param:
        type: string
      module_id:
        type: string
      state:
        enum:
        - 0
        - 1
        type: integer
      title:
        maxLength: 255
        minLength: 2
        type: string
      unique_key:
        maxLength: 20
        minLength: 2
        type: string
    required:
    - module_id
    - state
    - title
    - unique_key
    type: object
  dto.ProjectResponse:
    properties:
      code:
        type: string
      created_at:
        type: string
      description:
        type: string
      id:
        type: integer
      is_new_record:
        type: boolean
      module_ext_param:
        type: string
      module_id:
        type: string
      state:
        type: integer
      title:
        type: string
      unique_key:
        type: string
      updated_at:
        type: string
      user_id:
        type: integer
    type: object
  dto.ProjectUpdateRequest:
    properties:
      code:
        type: string
      description:
        maxLength: 255
        type: string
      module_ext_param:
        type: string
      module_id:
        type: string
      state:
        enum:
        - 0
        - 1
        type: integer
      title:
        maxLength: 255
        minLength: 2
        type: string
    type: object
  dto.RoleCreateDTO:
    properties:
      guard_name:
        type: string
      menu_ids:
        items:
          type: integer
        type: array
      name:
        type: string
      permission_ids:
        items:
          type: integer
        type: array
      remark:
        type: string
    required:
    - guard_name
    - name
    type: object
  dto.RoleDetailResponse:
    properties:
      guard_name:
        type: string
      id:
        type: integer
      is_admin:
        description: 是否为超级管理员
        type: boolean
      menus:
        items:
          $ref: '#/definitions/dto.RoleMenuResponse'
        type: array
      name:
        type: string
      permissions:
        items:
          $ref: '#/definitions/dto.RolePermissionResponse'
        type: array
      remark:
        type: string
    type: object
  dto.RoleItem:
    properties:
      id:
        type: integer
      name:
        type: string
      slug:
        type: string
    type: object
  dto.RoleMenuResponse:
    properties:
      icon:
        type: string
      id:
        type: integer
      order:
        type: integer
      parent_id:
        type: integer
      title:
        type: string
      uri:
        type: string
    type: object
  dto.RolePermissionResponse:
    properties:
      http_method:
        type: string
      http_path:
        type: string
      id:
        type: integer
      name:
        type: string
      order:
        type: integer
      parent_id:
        type: integer
      slug:
        type: string
    type: object
  dto.RoleResponse:
    properties:
      guard_name:
        type: string
      id:
        type: integer
      name:
        type: string
      permission_count:
        type: integer
      remark:
        type: string
    type: object
  dto.RoleUpdateDTO:
    properties:
      guard_name:
        type: string
      menu_ids:
        items:
          type: integer
        type: array
      name:
        type: string
      permission_ids:
        items:
          type: integer
        type: array
      remark:
        type: string
    type: object
  dto.ScheduledEmailRequest:
    properties:
      body:
        type: string
      delay_seconds:
        minimum: 0
        type: integer
      is_html:
        type: boolean
      subject:
        type: string
      to:
        type: string
    required:
    - body
    - subject
    - to
    type: object
  dto.SettingsResponse:
    properties:
      closed_message:
        description: 关闭提示信息
        type: string
      mail_from_email:
        description: 发件人邮箱
        type: string
      mail_from_name:
        description: 发件人名称
        type: string
      mail_host:
        description: 邮件服务器地址
        type: string
      mail_password:
        description: 邮件密码
        type: string
      mail_port:
        description: 邮件服务器端口
        type: integer
      mail_username:
        description: 邮件用户名
        type: string
      register_closed:
        description: 是否关闭注册
        type: boolean
      site_closed:
        description: 网站是否关闭
        type: boolean
      site_description:
        description: 网站描述
        type: string
      site_favicon:
        description: 网站Favicon
        type: string
      site_footer:
        description: 网站页脚
        type: string
      site_keywords:
        description: 网站关键词
        type: string
      site_logo:
        description: 网站Logo
        type: string
      site_name:
        description: 网站名称
        type: string
      site_notice:
        description: 网站公告
        type: string
    type: object
  dto.SettingsUpdateRequest:
    properties:
      closed_message:
        description: 关闭提示信息
        type: string
      mail_from_email:
        description: 发件人邮箱
        type: string
      mail_from_name:
        description: 发件人名称
        type: string
      mail_host:
        description: 邮件服务器地址
        type: string
      mail_password:
        description: 邮件密码
        type: string
      mail_port:
        description: 邮件服务器端口
        type: integer
      mail_username:
        description: 邮件用户名
        type: string
      register_closed:
        description: 是否关闭注册
        type: boolean
      site_closed:
        description: 网站是否关闭
        type: boolean
      site_description:
        description: 网站描述
        type: string
      site_favicon:
        description: 网站Favicon
        type: string
      site_footer:
        description: 网站页脚
        type: string
      site_keywords:
        description: 网站关键词
        type: string
      site_logo:
        description: 网站Logo
        type: string
      site_name:
        description: 网站名称
        type: string
      site_notice:
        description: 网站公告
        type: string
    type: object
  dto.StandardResponse:
    properties:
      code:
        description: 业务状态码，0表示成功
        type: integer
      data:
        description: 响应数据
      error:
        description: 错误信息，仅在开发环境显示
        type: string
      message:
        description: 响应消息
        type: string
    type: object
  dto.SystemInfoResponse:
    properties:
      database_version:
        description: 数据库版本
        type: string
      go_version:
        description: Go版本
        type: string
      server_time:
        description: 服务器时间
        type: string
      uptime:
        description: 运行时间
        type: string
      version:
        description: 系统版本
        type: string
    type: object
  dto.UpdateProfileDTO:
    properties:
      avatar:
        type: string
      email:
        maxLength: 100
        type: string
      name:
        maxLength: 50
        type: string
    required:
    - name
    type: object
  dto.UserResponse:
    properties:
      avatar:
        description: 头像
        type: string
      email:
        description: 邮箱
        type: string
      id:
        description: 用户ID
        type: integer
      name:
        description: 姓名
        type: string
      username:
        description: 用户名
        type: string
    type: object
host: localhost:3000
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: 这是使用Go Fiber构建的API服务.
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: Go Fiber API
  version: "1.0"
paths:
  /{unique_key}:
    get:
      description: 根据唯一键获取项目内容
      parameters:
      - description: 项目唯一键
        in: path
        name: unique_key
        required: true
        type: string
      produces:
      - text/html
      - text/plain
      responses:
        "200":
          description: 项目内容
          schema:
            type: string
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "403":
          description: 禁止访问
          schema:
            type: string
        "404":
          description: 内容未找到
          schema:
            type: string
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      summary: 获取唯一内容
      tags:
      - Gateway
  /{unique_key}/{rand_key}.jpg:
    get:
      description: 根据唯一键和随机键获取图片
      parameters:
      - description: 项目唯一键
        in: path
        name: unique_key
        required: true
        type: string
      - description: 随机键
        in: path
        name: rand_key
        required: true
        type: string
      produces:
      - image/jpeg
      responses:
        "200":
          description: 图片获取成功
          schema:
            type: file
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 图片未找到
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      summary: 获取唯一图片
      tags:
      - Gateway
  /admin/article/index:
    get:
      consumes:
      - application/json
      description: 获取所有公告列表，支持分页
      parameters:
      - description: 页码，默认为1
        in: query
        name: page
        type: integer
      - description: 每页条数，默认为10
        in: query
        name: page_size
        type: integer
      - description: 状态过滤，1-启用，0-禁用
        in: query
        name: status
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  allOf:
                  - $ref: '#/definitions/dto.PaginatedResponse'
                  - properties:
                      items:
                        items:
                          $ref: '#/definitions/dto.ArticleListItem'
                        type: array
                    type: object
              type: object
      security:
      - BearerAuth: []
      summary: 获取公告列表
      tags:
      - 公告管理
    post:
      consumes:
      - application/json
      description: 创建新的公告
      parameters:
      - description: 公告信息
        in: body
        name: article
        required: true
        schema:
          $ref: '#/definitions/dto.ArticleRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.ArticleResponse'
              type: object
      security:
      - BearerAuth: []
      summary: 创建公告
      tags:
      - 公告管理
  /admin/article/index/{id}:
    delete:
      consumes:
      - application/json
      description: 根据ID删除公告
      parameters:
      - description: 公告ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 删除公告
      tags:
      - 公告管理
    get:
      consumes:
      - application/json
      description: 根据ID获取公告详情
      parameters:
      - description: 公告ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.ArticleResponse'
              type: object
      security:
      - BearerAuth: []
      summary: 获取公告详情
      tags:
      - 公告管理
    put:
      consumes:
      - application/json
      description: 更新现有公告信息
      parameters:
      - description: 公告ID
        in: path
        name: id
        required: true
        type: integer
      - description: 公告更新信息
        in: body
        name: article
        required: true
        schema:
          $ref: '#/definitions/dto.ArticleUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.ArticleResponse'
              type: object
      security:
      - BearerAuth: []
      summary: 更新公告
      tags:
      - 公告管理
  /admin/article/publish/{id}:
    post:
      consumes:
      - application/json
      description: 将公告状态设置为已发布并通过队列发送通知
      parameters:
      - description: 公告ID
        in: path
        name: id
        required: true
        type: integer
      - description: 延迟发布时间(分钟)
        in: query
        name: delay_minutes
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.ArticleResponse'
              type: object
      security:
      - BearerAuth: []
      summary: 发布公告
      tags:
      - 公告管理
  /admin/auth/captcha:
    get:
      description: 生成一个新的图片验证码，返回验证码密钥和Base64编码的图片
      produces:
      - application/json
      responses:
        "200":
          description: 成功响应
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.CaptchaResponse'
              type: object
        "500":
          description: 生成验证码失败
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      summary: 获取验证码
      tags:
      - Authentication
  /admin/auth/forgot:
    post:
      consumes:
      - application/json
      description: 处理用户忘记密码请求，向用户邮箱发送重置密码链接
      parameters:
      - description: 忘记密码请求信息
        in: body
        name: forgotPassword
        required: true
        schema:
          $ref: '#/definitions/dto.AuthForgotPasswordDTO'
      produces:
      - application/json
      responses:
        "200":
          description: 成功发送密码重置邮件
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "400":
          description: 请求参数错误或验证码错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 用户名或邮箱不匹配
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "500":
          description: 服务器内部错误或邮件发送失败
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      summary: 忘记密码
      tags:
      - Authentication
  /admin/auth/login:
    post:
      consumes:
      - application/json
      description: 使用用户名、密码和验证码登录并获取JWT令牌
      parameters:
      - description: 登录凭证 (包含验证码)
        in: body
        name: login
        required: true
        schema:
          $ref: '#/definitions/dto.AuthLoginDTO'
      produces:
      - application/json
      responses:
        "200":
          description: 登录成功，返回JWT令牌
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.AuthResponse'
              type: object
        "400":
          description: 请求参数错误或验证码错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "401":
          description: 用户名或密码错误或账户被禁用
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "500":
          description: 服务器内部错误或令牌生成失败
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      summary: 管理员登录
      tags:
      - Authentication
  /admin/auth/reset:
    post:
      consumes:
      - application/json
      description: 使用重置令牌重置用户密码
      parameters:
      - description: 重置密码请求信息
        in: body
        name: resetPassword
        required: true
        schema:
          $ref: '#/definitions/dto.AuthResetPasswordDTO'
      produces:
      - application/json
      responses:
        "200":
          description: 密码重置成功
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "400":
          description: 请求参数错误或令牌无效
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 用户不存在
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      summary: 重置密码
      tags:
      - Authentication
  /admin/auth/signup:
    post:
      consumes:
      - application/json
      description: 创建一个新的管理员账户
      parameters:
      - description: 注册信息 (referee根据配置决定是否必需)
        in: body
        name: signup
        required: true
        schema:
          $ref: '#/definitions/dto.AuthSignupDTO'
      produces:
      - application/json
      responses:
        "200":
          description: 注册成功，返回用户ID
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  properties:
                    id:
                      type: integer
                  type: object
              type: object
        "400":
          description: 请求参数错误、验证码错误、邀请码错误或用户名/邮箱已存在
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      summary: 管理员注册
      tags:
      - Authentication
  /admin/auth/verify:
    get:
      consumes:
      - application/json
      description: 验证JWT令牌是否有效
      produces:
      - application/json
      responses:
        "200":
          description: 验证结果
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  properties:
                    valid:
                      type: boolean
                  type: object
              type: object
        "401":
          description: 令牌无效
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - ApiKeyAuth: []
      summary: 验证Token
      tags:
      - Authentication
  /admin/dashboard:
    get:
      consumes:
      - application/json
      description: 获取系统概览数据，包括用户数、角色数、权限数等
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.DashboardDataResponse'
              type: object
      security:
      - BearerAuth: []
      summary: 获取控制面板数据
      tags:
      - 控制面板
  /admin/dashboard/logs:
    get:
      consumes:
      - application/json
      description: 获取系统中的最近操作日志
      parameters:
      - description: 限制数量，默认为10
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/dto.OperationLogResponse'
                  type: array
              type: object
      security:
      - BearerAuth: []
      summary: 获取最近操作日志
      tags:
      - 控制面板
  /admin/dashboard/system:
    get:
      consumes:
      - application/json
      description: 获取系统运行信息，包括服务器状态、数据库状态等
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.SystemInfoResponse'
              type: object
      security:
      - BearerAuth: []
      summary: 获取系统信息
      tags:
      - 控制面板
  /admin/domain/index:
    get:
      consumes:
      - application/json
      description: 获取域名列表，支持分页和筛选
      parameters:
      - description: 页码，默认为1
        in: query
        name: page
        type: integer
      - description: 每页条数，默认为10
        in: query
        name: page_size
        type: integer
      - description: 域名(模糊搜索)
        in: query
        name: domain
        type: string
      - description: 类型：10 过滤域名/20 切换使用的域名
        in: query
        name: type
        type: integer
      - description: 状态
        in: query
        name: state
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  allOf:
                  - $ref: '#/definitions/dto.PaginatedResponse'
                  - properties:
                      items:
                        items:
                          $ref: '#/definitions/dto.DomainListItem'
                        type: array
                    type: object
              type: object
      security:
      - BearerAuth: []
      summary: 获取域名列表
      tags:
      - 域名管理
    post:
      consumes:
      - application/json
      description: 创建新的域名
      parameters:
      - description: 域名信息
        in: body
        name: domain
        required: true
        schema:
          $ref: '#/definitions/dto.DomainRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.DomainResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 创建域名
      tags:
      - 域名管理
  /admin/domain/index/{id}:
    delete:
      consumes:
      - application/json
      description: 删除现有域名
      parameters:
      - description: 域名ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 域名不存在
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 删除域名
      tags:
      - 域名管理
    get:
      consumes:
      - application/json
      description: 根据ID获取域名详情
      parameters:
      - description: 域名ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.DomainResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 域名不存在
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 获取域名详情
      tags:
      - 域名管理
    put:
      consumes:
      - application/json
      description: 更新现有域名
      parameters:
      - description: 域名ID
        in: path
        name: id
        required: true
        type: integer
      - description: 域名更新信息
        in: body
        name: domain
        required: true
        schema:
          $ref: '#/definitions/dto.DomainUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.DomainResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 域名不存在
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 更新域名
      tags:
      - 域名管理
  /admin/failed-job/clear-all:
    delete:
      consumes:
      - application/json
      description: 清空所有失败任务
      produces:
      - application/json
      responses:
        "200":
          description: 清空成功
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 清空所有失败任务
      tags:
      - 任务管理
  /admin/failed-job/index:
    get:
      consumes:
      - application/json
      description: 获取失败任务列表，支持分页和筛选
      parameters:
      - description: 队列名称(模糊搜索)
        in: query
        name: queue
        type: string
      - description: 连接名称(模糊搜索)
        in: query
        name: connection
        type: string
      - description: '开始时间(格式: 2006-01-02 15:04:05)'
        in: query
        name: start_time
        type: string
      - description: '结束时间(格式: 2006-01-02 15:04:05)'
        in: query
        name: end_time
        type: string
      - description: '页码(默认: 1)'
        in: query
        name: page
        type: integer
      - description: '每页数量(默认: 10)'
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/dto.FailedJobListItem'
                  type: array
              type: object
      security:
      - BearerAuth: []
      summary: 获取失败任务列表
      tags:
      - 任务管理
    post:
      consumes:
      - application/json
      description: 创建新的失败任务
      parameters:
      - description: 失败任务信息
        in: body
        name: failedJob
        required: true
        schema:
          $ref: '#/definitions/dto.FailedJobCreateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.FailedJobResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 创建失败任务
      tags:
      - 任务管理
  /admin/failed-job/index/{id}:
    delete:
      consumes:
      - application/json
      description: 删除现有失败任务
      parameters:
      - description: 失败任务ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 失败任务不存在
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 删除失败任务
      tags:
      - 任务管理
    get:
      consumes:
      - application/json
      description: 根据ID获取失败任务详情
      parameters:
      - description: 失败任务ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.FailedJobResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 失败任务不存在
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 获取失败任务详情
      tags:
      - 任务管理
    put:
      consumes:
      - application/json
      description: 更新现有失败任务
      parameters:
      - description: 失败任务ID
        in: path
        name: id
        required: true
        type: integer
      - description: 失败任务更新信息
        in: body
        name: failedJob
        required: true
        schema:
          $ref: '#/definitions/dto.FailedJobUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.FailedJobResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 失败任务不存在
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 更新失败任务
      tags:
      - 任务管理
  /admin/failed-job/retry/{id}:
    post:
      consumes:
      - application/json
      description: 重试失败的任务
      parameters:
      - description: 失败任务ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 重试成功
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 失败任务不存在
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 重试失败任务
      tags:
      - 任务管理
  /admin/friendly-list:
    get:
      consumes:
      - application/json
      description: 获取公开可用的友情链接列表，无需认证
      parameters:
      - description: 页码，默认为1
        in: query
        name: page
        type: integer
      - description: 每页条数，默认为20
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/dto.FriendlyListItem'
                  type: array
              type: object
      summary: 获取公开友情链接列表
      tags:
      - 公开接口
  /admin/friendly/index:
    get:
      consumes:
      - application/json
      description: 获取友情链接列表，支持分页和筛选
      parameters:
      - description: 页码，默认为1
        in: query
        name: page
        type: integer
      - description: 每页条数，默认为10
        in: query
        name: page_size
        type: integer
      - description: 名称(模糊搜索)
        in: query
        name: name
        type: string
      - description: 类型
        in: query
        name: type
        type: integer
      - description: 状态
        in: query
        name: state
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  allOf:
                  - $ref: '#/definitions/dto.PaginatedResponse'
                  - properties:
                      items:
                        items:
                          $ref: '#/definitions/dto.FriendlyListItem'
                        type: array
                    type: object
              type: object
      security:
      - BearerAuth: []
      summary: 获取友情链接列表
      tags:
      - 友情链接管理
    post:
      consumes:
      - application/json
      description: 创建新的友情链接
      parameters:
      - description: 友情链接信息
        in: body
        name: friendly
        required: true
        schema:
          $ref: '#/definitions/dto.FriendlyRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.FriendlyResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 创建友情链接
      tags:
      - 友情链接管理
  /admin/friendly/index/{id}:
    delete:
      consumes:
      - application/json
      description: 删除现有友情链接
      parameters:
      - description: 友情链接ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 友情链接不存在
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 删除友情链接
      tags:
      - 友情链接管理
    get:
      consumes:
      - application/json
      description: 根据ID获取友情链接详情
      parameters:
      - description: 友情链接ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.FriendlyResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 友情链接不存在
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 获取友情链接详情
      tags:
      - 友情链接管理
    put:
      consumes:
      - application/json
      description: 更新现有友情链接
      parameters:
      - description: 友情链接ID
        in: path
        name: id
        required: true
        type: integer
      - description: 友情链接更新信息
        in: body
        name: friendly
        required: true
        schema:
          $ref: '#/definitions/dto.FriendlyUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.FriendlyResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 友情链接不存在
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 更新友情链接
      tags:
      - 友情链接管理
  /admin/invitation/batch:
    post:
      consumes:
      - application/json
      description: 批量启用/禁用/删除邀请码
      parameters:
      - description: 批量操作请求
        in: body
        name: batch
        required: true
        schema:
          $ref: '#/definitions/dto.InvitationBatchRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 操作成功
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 批量操作邀请码
      tags:
      - 邀请码管理
  /admin/invitation/index:
    get:
      consumes:
      - application/json
      description: 获取邀请码列表，支持分页和筛选
      parameters:
      - description: 页码，默认为1
        in: query
        name: page
        type: integer
      - description: 每页条数，默认为10
        in: query
        name: page_size
        type: integer
      - description: 邀请码(精确匹配)
        in: query
        name: code
        type: string
      - description: 状态
        in: query
        name: state
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  allOf:
                  - $ref: '#/definitions/dto.PaginatedResponse'
                  - properties:
                      items:
                        items:
                          $ref: '#/definitions/dto.InvitationListItem'
                        type: array
                    type: object
              type: object
      security:
      - BearerAuth: []
      summary: 获取邀请码列表
      tags:
      - 邀请码管理
    post:
      consumes:
      - application/json
      description: 创建新的邀请码，支持批量生成
      parameters:
      - description: 邀请码信息
        in: body
        name: invitation
        required: true
        schema:
          $ref: '#/definitions/dto.InvitationRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/dto.InvitationResponse'
                  type: array
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 创建邀请码
      tags:
      - 邀请码管理
  /admin/invitation/index/{id}:
    delete:
      consumes:
      - application/json
      description: 删除现有邀请码
      parameters:
      - description: 邀请码ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 邀请码不存在
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 删除邀请码
      tags:
      - 邀请码管理
    get:
      consumes:
      - application/json
      description: 根据ID获取邀请码详情
      parameters:
      - description: 邀请码ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.InvitationResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 邀请码不存在
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 获取邀请码详情
      tags:
      - 邀请码管理
    put:
      consumes:
      - application/json
      description: 更新现有邀请码
      parameters:
      - description: 邀请码ID
        in: path
        name: id
        required: true
        type: integer
      - description: 邀请码更新信息
        in: body
        name: invitation
        required: true
        schema:
          $ref: '#/definitions/dto.InvitationUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.InvitationResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 邀请码不存在
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 更新邀请码
      tags:
      - 邀请码管理
  /admin/job/clear-all:
    delete:
      consumes:
      - application/json
      description: 清空所有任务
      produces:
      - application/json
      responses:
        "200":
          description: 清空成功
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 清空所有任务
      tags:
      - 任务管理
  /admin/job/index:
    get:
      consumes:
      - application/json
      description: 获取任务列表，支持分页和筛选
      parameters:
      - description: 队列名称(模糊搜索)
        in: query
        name: queue
        type: string
      - description: 尝试次数
        in: query
        name: attempts
        type: integer
      - description: 是否已保留
        in: query
        name: is_reserved
        type: boolean
      - description: '页码(默认: 1)'
        in: query
        name: page
        type: integer
      - description: '每页数量(默认: 10)'
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/dto.JobListItem'
                  type: array
              type: object
      security:
      - BearerAuth: []
      summary: 获取任务列表
      tags:
      - 任务管理
    post:
      consumes:
      - application/json
      description: 创建新的任务
      parameters:
      - description: 任务信息
        in: body
        name: job
        required: true
        schema:
          $ref: '#/definitions/dto.JobCreateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.JobResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 创建任务
      tags:
      - 任务管理
  /admin/job/index/{id}:
    delete:
      consumes:
      - application/json
      description: 删除现有任务
      parameters:
      - description: 任务ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 任务不存在
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 删除任务
      tags:
      - 任务管理
    get:
      consumes:
      - application/json
      description: 根据ID获取任务详情
      parameters:
      - description: 任务ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.JobResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 任务不存在
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 获取任务详情
      tags:
      - 任务管理
    put:
      consumes:
      - application/json
      description: 更新现有任务
      parameters:
      - description: 任务ID
        in: path
        name: id
        required: true
        type: integer
      - description: 任务更新信息
        in: body
        name: job
        required: true
        schema:
          $ref: '#/definitions/dto.JobUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.JobResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 任务不存在
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 更新任务
      tags:
      - 任务管理
  /admin/menu/index:
    get:
      consumes:
      - application/json
      description: 获取系统中的所有菜单
      parameters:
      - description: 菜单标题(模糊搜索)
        in: query
        name: title
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/dto.MenuResponse'
                  type: array
              type: object
      security:
      - BearerAuth: []
      summary: 获取菜单列表
      tags:
      - 菜单管理
    post:
      consumes:
      - application/json
      description: 创建一个新的菜单
      parameters:
      - description: 菜单信息
        in: body
        name: menu
        required: true
        schema:
          $ref: '#/definitions/dto.MenuCreateDTO'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.MenuResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 创建菜单
      tags:
      - 菜单管理
  /admin/menu/index/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定的菜单
      parameters:
      - description: 菜单ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 菜单不存在
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 删除菜单
      tags:
      - 菜单管理
    get:
      consumes:
      - application/json
      description: 获取指定ID的菜单详情
      parameters:
      - description: 菜单ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.MenuDetailResponse'
              type: object
        "404":
          description: 菜单不存在
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 获取菜单详情
      tags:
      - 菜单管理
    put:
      consumes:
      - application/json
      description: 更新已存在的菜单信息
      parameters:
      - description: 菜单ID
        in: path
        name: id
        required: true
        type: integer
      - description: 菜单更新信息
        in: body
        name: menu
        required: true
        schema:
          $ref: '#/definitions/dto.MenuUpdateDTO'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.MenuResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 菜单不存在
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 更新菜单
      tags:
      - 菜单管理
  /admin/menu/tree:
    get:
      consumes:
      - application/json
      description: 获取当前登录用户有权限访问的菜单树形结构
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/dto.MenuResponse'
                  type: array
              type: object
      security:
      - BearerAuth: []
      summary: 获取用户菜单树
      tags:
      - 菜单管理
  /admin/module/index:
    get:
      consumes:
      - application/json
      description: 获取模块列表，支持分页和筛选
      parameters:
      - description: 页码，默认为1
        in: query
        name: page
        type: integer
      - description: 每页条数，默认为10
        in: query
        name: page_size
        type: integer
      - description: 模块名称(模糊搜索)
        in: query
        name: title
        type: string
      - description: 安全等级
        in: query
        name: level
        type: integer
      - description: 状态
        in: query
        name: state
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  allOf:
                  - $ref: '#/definitions/dto.PaginatedResponse'
                  - properties:
                      items:
                        items:
                          $ref: '#/definitions/dto.ModuleListItem'
                        type: array
                    type: object
              type: object
      security:
      - BearerAuth: []
      summary: 获取模块列表
      tags:
      - 模块管理
    post:
      consumes:
      - application/json
      description: 创建新的模块
      parameters:
      - description: 模块信息
        in: body
        name: module
        required: true
        schema:
          $ref: '#/definitions/dto.ModuleRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.ModuleResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 创建模块
      tags:
      - 模块管理
  /admin/module/index/{id}:
    delete:
      consumes:
      - application/json
      description: 删除现有模块
      parameters:
      - description: 模块ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 模块不存在
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 删除模块
      tags:
      - 模块管理
    get:
      consumes:
      - application/json
      description: 根据ID获取模块详情
      parameters:
      - description: 模块ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.ModuleResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 模块不存在
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 获取模块详情
      tags:
      - 模块管理
    put:
      consumes:
      - application/json
      description: 更新现有模块
      parameters:
      - description: 模块ID
        in: path
        name: id
        required: true
        type: integer
      - description: 模块更新信息
        in: body
        name: module
        required: true
        schema:
          $ref: '#/definitions/dto.ModuleUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.ModuleResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 模块不存在
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 更新模块
      tags:
      - 模块管理
  /admin/permissions:
    get:
      consumes:
      - application/json
      description: 获取系统中的所有权限
      parameters:
      - description: 权限名称(模糊搜索)
        in: query
        name: name
        type: string
      - description: 父权限ID
        in: query
        name: parent_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/dto.PermissionResponse'
                  type: array
              type: object
      security:
      - BearerAuth: []
      summary: 获取权限列表
      tags:
      - 权限管理
    post:
      consumes:
      - application/json
      description: 创建一个新的权限
      parameters:
      - description: 权限信息
        in: body
        name: permission
        required: true
        schema:
          $ref: '#/definitions/dto.PermissionCreateDTO'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.PermissionResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 创建权限
      tags:
      - 权限管理
  /admin/permissions/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定的权限
      parameters:
      - description: 权限ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 权限不存在
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 删除权限
      tags:
      - 权限管理
    get:
      consumes:
      - application/json
      description: 获取指定ID的权限详情
      parameters:
      - description: 权限ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.PermissionDetailResponse'
              type: object
        "404":
          description: 权限不存在
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 获取权限详情
      tags:
      - 权限管理
    put:
      consumes:
      - application/json
      description: 更新已存在的权限信息
      parameters:
      - description: 权限ID
        in: path
        name: id
        required: true
        type: integer
      - description: 权限更新信息
        in: body
        name: permission
        required: true
        schema:
          $ref: '#/definitions/dto.PermissionUpdateDTO'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.PermissionResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 权限不存在
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 更新权限
      tags:
      - 权限管理
  /admin/permissions/batch-delete:
    post:
      consumes:
      - application/json
      description: 批量删除指定ID的权限
      parameters:
      - description: 权限ID列表
        in: body
        name: ids
        required: true
        schema:
          $ref: '#/definitions/dto.BatchIDsDTO'
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 批量删除权限
      tags:
      - 权限管理
  /admin/permissions/check/{slug}:
    get:
      consumes:
      - application/json
      description: 检查当前用户是否有特定的权限标识
      parameters:
      - description: 权限标识
        in: path
        name: slug
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  type: boolean
              type: object
      security:
      - BearerAuth: []
      summary: 检查用户权限
      tags:
      - 权限管理
  /admin/permissions/tree:
    get:
      consumes:
      - application/json
      description: 获取系统中的所有权限，以树形结构返回
      parameters:
      - description: 排除的权限ID，多个ID用逗号分隔
        in: query
        name: exclude_ids
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/dto.PermissionResponse'
                  type: array
              type: object
      security:
      - BearerAuth: []
      summary: 获取权限树
      tags:
      - 权限管理
  /admin/project/my:
    get:
      consumes:
      - application/json
      description: 获取当前用户的项目列表，支持分页
      parameters:
      - description: 页码，默认为1
        in: query
        name: page
        type: integer
      - description: 每页条数，默认为10
        in: query
        name: page_size
        type: integer
      - description: 状态过滤，1-启用，0-禁用
        in: query
        name: state
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  allOf:
                  - $ref: '#/definitions/dto.PaginatedResponse'
                  - properties:
                      items:
                        items:
                          $ref: '#/definitions/dto.ProjectListItem'
                        type: array
                    type: object
              type: object
      security:
      - BearerAuth: []
      summary: 获取项目列表
      tags:
      - 项目管理
    post:
      consumes:
      - application/json
      description: 创建新的项目
      parameters:
      - description: 项目信息
        in: body
        name: project
        required: true
        schema:
          $ref: '#/definitions/dto.ProjectRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.ProjectResponse'
              type: object
      security:
      - BearerAuth: []
      summary: 创建项目
      tags:
      - 项目管理
  /admin/project/my/{id}:
    delete:
      consumes:
      - application/json
      description: 根据ID删除项目
      parameters:
      - description: 项目ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 删除项目
      tags:
      - 项目管理
    get:
      consumes:
      - application/json
      description: 根据ID获取项目详情
      parameters:
      - description: 项目ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.ProjectResponse'
              type: object
      security:
      - BearerAuth: []
      summary: 获取项目详情
      tags:
      - 项目管理
    put:
      consumes:
      - application/json
      description: 更新现有项目信息
      parameters:
      - description: 项目ID
        in: path
        name: id
        required: true
        type: integer
      - description: 项目更新信息
        in: body
        name: project
        required: true
        schema:
          $ref: '#/definitions/dto.ProjectUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.ProjectResponse'
              type: object
      security:
      - BearerAuth: []
      summary: 更新项目
      tags:
      - 项目管理
  /admin/project/my/{id}/toggle:
    put:
      consumes:
      - application/json
      description: 切换项目的启用/禁用状态
      parameters:
      - description: 项目ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.ProjectResponse'
              type: object
      security:
      - BearerAuth: []
      summary: 切换项目状态
      tags:
      - 项目管理
  /admin/project/my/{userid}/{projectid}:
    delete:
      consumes:
      - application/json
      description: 根据用户ID和项目ID删除项目内容
      parameters:
      - description: 用户ID
        in: path
        name: userid
        required: true
        type: integer
      - description: 项目ID
        in: path
        name: projectid
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 删除项目内容
      tags:
      - 项目内容管理
    get:
      consumes:
      - application/json
      description: 根据用户ID和项目ID获取项目内容详情
      parameters:
      - description: 用户ID
        in: path
        name: userid
        required: true
        type: integer
      - description: 项目ID
        in: path
        name: projectid
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.ProjectContentResponse'
              type: object
      security:
      - BearerAuth: []
      summary: 获取项目内容详情
      tags:
      - 项目内容管理
  /admin/project/my/preview:
    get:
      consumes:
      - application/json
      description: 预览项目内容
      parameters:
      - description: 项目ID
        in: query
        name: project_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 预览项目
      tags:
      - 项目管理
  /admin/project/viewcode/{projectid}:
    get:
      consumes:
      - application/json
      description: 根据项目ID查看代码
      parameters:
      - description: 项目ID
        in: path
        name: projectid
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  type: string
              type: object
      security:
      - BearerAuth: []
      summary: 查看项目代码
      tags:
      - 项目管理
  /admin/projectscontent/index:
    get:
      consumes:
      - application/json
      description: 获取项目内容列表，支持分页
      parameters:
      - description: 页码，默认为1
        in: query
        name: page
        type: integer
      - description: 每页条数，默认为10
        in: query
        name: page_size
        type: integer
      - description: 项目ID过滤
        in: query
        name: project_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  allOf:
                  - $ref: '#/definitions/dto.PaginatedResponse'
                  - properties:
                      items:
                        items:
                          $ref: '#/definitions/dto.ProjectContentListItem'
                        type: array
                    type: object
              type: object
      security:
      - BearerAuth: []
      summary: 获取项目内容列表
      tags:
      - 项目内容管理
    post:
      consumes:
      - application/json
      description: 创建新的项目内容
      parameters:
      - description: 项目内容信息
        in: body
        name: content
        required: true
        schema:
          $ref: '#/definitions/dto.ProjectContentRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.ProjectContentResponse'
              type: object
      security:
      - BearerAuth: []
      summary: 创建项目内容
      tags:
      - 项目内容管理
  /admin/projectscontent/index/{id}:
    delete:
      consumes:
      - application/json
      description: 根据ID删除项目内容
      parameters:
      - description: 内容ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 删除项目内容
      tags:
      - 项目内容管理
    get:
      consumes:
      - application/json
      description: 根据ID获取项目内容详情
      parameters:
      - description: 项目内容ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.ProjectContentResponse'
              type: object
      security:
      - BearerAuth: []
      summary: 获取项目内容详情
      tags:
      - 项目内容管理
    put:
      consumes:
      - application/json
      description: 更新现有项目内容
      parameters:
      - description: 内容ID
        in: path
        name: id
        required: true
        type: integer
      - description: 项目内容更新信息
        in: body
        name: content
        required: true
        schema:
          $ref: '#/definitions/dto.ProjectContentUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.ProjectContentResponse'
              type: object
      security:
      - BearerAuth: []
      summary: 更新项目内容
      tags:
      - 项目内容管理
  /admin/role/{id}/menus:
    post:
      consumes:
      - application/json
      description: 批量分配菜单给指定角色
      parameters:
      - description: 角色ID
        in: path
        name: id
        required: true
        type: integer
      - description: 菜单ID列表
        in: body
        name: menu_ids
        required: true
        schema:
          $ref: '#/definitions/dto.BatchIDsDTO'
      produces:
      - application/json
      responses:
        "200":
          description: 分配成功
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 角色不存在
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 分配菜单给角色
      tags:
      - 角色管理
  /admin/role/{id}/permissions:
    post:
      consumes:
      - application/json
      description: 批量分配权限给指定角色
      parameters:
      - description: 角色ID
        in: path
        name: id
        required: true
        type: integer
      - description: 权限ID列表
        in: body
        name: permission_ids
        required: true
        schema:
          $ref: '#/definitions/dto.BatchIDsDTO'
      produces:
      - application/json
      responses:
        "200":
          description: 分配成功
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 角色不存在
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 分配权限给角色
      tags:
      - 角色管理
  /admin/role/{id}/users:
    get:
      consumes:
      - application/json
      description: 获取拥有指定角色的用户列表
      parameters:
      - description: 角色ID
        in: path
        name: id
        required: true
        type: integer
      - description: 页码，默认为1
        in: query
        name: page
        type: integer
      - description: 每页条数，默认为10
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  allOf:
                  - $ref: '#/definitions/dto.PaginatedResponse'
                  - properties:
                      items:
                        items:
                          $ref: '#/definitions/dto.UserResponse'
                        type: array
                    type: object
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 角色不存在
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 获取角色用户列表
      tags:
      - 角色管理
    post:
      consumes:
      - application/json
      description: 将指定角色分配给多个用户
      parameters:
      - description: 角色ID
        in: path
        name: id
        required: true
        type: integer
      - description: 用户ID列表
        in: body
        name: user_ids
        required: true
        schema:
          $ref: '#/definitions/dto.BatchIDsDTO'
      produces:
      - application/json
      responses:
        "200":
          description: 分配成功
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 角色不存在
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 分配角色给用户
      tags:
      - 角色管理
  /admin/role/{id}/users/remove:
    post:
      consumes:
      - application/json
      description: 从多个用户移除指定角色
      parameters:
      - description: 角色ID
        in: path
        name: id
        required: true
        type: integer
      - description: 用户ID列表
        in: body
        name: user_ids
        required: true
        schema:
          $ref: '#/definitions/dto.BatchIDsDTO'
      produces:
      - application/json
      responses:
        "200":
          description: 移除成功
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 角色不存在
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 从用户移除角色
      tags:
      - 角色管理
  /admin/role/all:
    get:
      consumes:
      - application/json
      description: 获取系统中的所有角色，不分页
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/dto.RoleResponse'
                  type: array
              type: object
      security:
      - BearerAuth: []
      summary: 获取所有角色
      tags:
      - 角色管理
  /admin/role/batch-delete:
    post:
      consumes:
      - application/json
      description: 批量删除指定ID的角色(管理员角色除外)
      parameters:
      - description: 角色ID列表
        in: body
        name: ids
        required: true
        schema:
          $ref: '#/definitions/dto.BatchIDsDTO'
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 批量删除角色
      tags:
      - 角色管理
  /admin/role/index:
    get:
      consumes:
      - application/json
      description: 获取角色列表，支持分页和筛选
      parameters:
      - description: 页码，默认为1
        in: query
        name: page
        type: integer
      - description: 每页条数，默认为10
        in: query
        name: page_size
        type: integer
      - description: 角色名称(模糊搜索)
        in: query
        name: name
        type: string
      - description: Guard名称
        in: query
        name: guard_name
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  allOf:
                  - $ref: '#/definitions/dto.PaginatedResponse'
                  - properties:
                      items:
                        items:
                          $ref: '#/definitions/dto.RoleResponse'
                        type: array
                    type: object
              type: object
      security:
      - BearerAuth: []
      summary: 获取角色列表
      tags:
      - 角色管理
    post:
      consumes:
      - application/json
      description: 创建新的角色
      parameters:
      - description: 角色信息
        in: body
        name: role
        required: true
        schema:
          $ref: '#/definitions/dto.RoleCreateDTO'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.RoleResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 创建角色
      tags:
      - 角色管理
  /admin/role/index/{id}:
    delete:
      consumes:
      - application/json
      description: 删除现有角色
      parameters:
      - description: 角色ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 角色不存在
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 删除角色
      tags:
      - 角色管理
    get:
      consumes:
      - application/json
      description: 根据ID获取角色详情，包含权限树形结构
      parameters:
      - description: 角色ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.RoleDetailResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 角色不存在
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 获取角色详情
      tags:
      - 角色管理
    put:
      consumes:
      - application/json
      description: 更新现有角色
      parameters:
      - description: 角色ID
        in: path
        name: id
        required: true
        type: integer
      - description: 角色更新信息
        in: body
        name: role
        required: true
        schema:
          $ref: '#/definitions/dto.RoleUpdateDTO'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.RoleResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 角色不存在
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 更新角色
      tags:
      - 角色管理
  /admin/settings:
    get:
      consumes:
      - application/json
      description: 获取所有网站设置
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.SettingsResponse'
              type: object
      security:
      - BearerAuth: []
      summary: 获取网站设置
      tags:
      - 网站设置
    put:
      consumes:
      - application/json
      description: 更新网站设置
      parameters:
      - description: 网站设置信息
        in: body
        name: settings
        required: true
        schema:
          $ref: '#/definitions/dto.SettingsUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.SettingsResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 更新网站设置
      tags:
      - 网站设置
  /admin/users:
    get:
      consumes:
      - application/json
      description: 获取管理员用户列表，支持分页和按用户名、姓名、邮箱、状态过滤
      parameters:
      - description: 用户名 (模糊查询)
        in: query
        name: username
        type: string
      - description: 姓名 (模糊查询)
        in: query
        name: name
        type: string
      - description: 邮箱 (模糊查询)
        in: query
        name: email
        type: string
      - description: 状态 (1:启用, 其他或不传:所有)
        in: query
        name: state
        type: integer
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 10
        description: 每页数量
        in: query
        maximum: 100
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功响应
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  allOf:
                  - $ref: '#/definitions/dto.PaginatedResponse'
                  - properties:
                      items:
                        items:
                          $ref: '#/definitions/dto.AdminUserResponse'
                        type: array
                    type: object
              type: object
        "400":
          description: 无效的查询参数
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 获取管理员用户列表
      tags:
      - AdminUsers
    post:
      consumes:
      - application/json
      description: 创建一个新的管理员用户
      parameters:
      - description: 创建用户请求体
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/dto.AdminUserCreateDTO'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功，返回用户ID
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  properties:
                    id:
                      type: integer
                  type: object
              type: object
        "400":
          description: 无效的请求数据或用户名已存在
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 创建管理员用户
      tags:
      - AdminUsers
  /admin/users/{id}:
    delete:
      consumes:
      - application/json
      description: 根据ID删除管理员用户 (软删除)
      parameters:
      - description: 用户ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 用户未找到
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 删除管理员用户
      tags:
      - AdminUsers
    get:
      consumes:
      - application/json
      description: 根据ID获取管理员用户的详细信息
      parameters:
      - description: 用户ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功响应
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.AdminUserResponse'
              type: object
        "404":
          description: 用户未找到
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 获取单个管理员用户
      tags:
      - AdminUsers
    put:
      consumes:
      - application/json
      description: 根据ID更新管理员用户的信息
      parameters:
      - description: 用户ID
        in: path
        name: id
        required: true
        type: integer
      - description: 更新用户请求体 (只需提供要更新的字段)
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/dto.AdminUserUpdateDTO'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "400":
          description: 无效的用户ID、请求数据或用户名已被使用
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 用户未找到
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 更新管理员用户
      tags:
      - AdminUsers
  /auth/profile:
    get:
      consumes:
      - application/json
      description: 获取当前登录用户的详细信息包括权限
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.ProfileResponse'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 获取用户信息
      tags:
      - 认证管理
    put:
      consumes:
      - application/json
      description: 更新当前登录用户的个人信息
      parameters:
      - description: 用户信息
        in: body
        name: profile
        required: true
        schema:
          $ref: '#/definitions/dto.UpdateProfileDTO'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/dto.ProfileResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      security:
      - BearerAuth: []
      summary: 更新用户信息
      tags:
      - 认证管理
  /download/{hash}:
    get:
      description: 根据哈希值下载文件
      parameters:
      - description: 文件哈希值
        in: path
        name: hash
        required: true
        type: string
      produces:
      - application/octet-stream
      - text/plain
      responses:
        "200":
          description: 文件下载成功
          schema:
            type: file
        "404":
          description: 文件未找到
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      summary: 下载文件
      tags:
      - Gateway
  /emails/scheduled:
    post:
      consumes:
      - application/json
      description: 将邮件请求加入队列，支持延迟发送
      parameters:
      - description: 定时邮件请求体
        in: body
        name: email
        required: true
        schema:
          $ref: '#/definitions/dto.ScheduledEmailRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 邮件已加入发送队列
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      summary: 发送定时邮件
      tags:
      - Email
  /emails/send:
    post:
      consumes:
      - application/json
      description: 立即将邮件请求加入发送队列
      parameters:
      - description: 邮件请求体
        in: body
        name: email
        required: true
        schema:
          $ref: '#/definitions/dto.EmailRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 邮件已加入发送队列
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      summary: 发送邮件
      tags:
      - Email
  /keepsession:
    get:
      description: 保持客户端会话活跃状态
      produces:
      - text/plain
      responses:
        "200":
          description: 会话保持成功
          schema:
            type: string
      summary: 保持会话
      tags:
      - Gateway
  /logs:
    get:
      description: 返回日志查看欢迎页面
      produces:
      - application/json
      responses:
        "200":
          description: 成功响应
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      summary: 查看日志
      tags:
      - Gateway
  /queue/stats:
    get:
      consumes:
      - application/json
      description: 获取当前所有队列的统计数据（如任务数、延迟等）
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  type: object
              type: object
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      summary: 获取队列统计信息
      tags:
      - Queue
  /submit:
    get:
      consumes:
      - multipart/form-data
      - application/json
      - application/x-www-form-urlencoded
      description: 接收并存储项目相关数据
      parameters:
      - description: 项目唯一键
        in: query
        name: unique_key
        type: string
      - description: 项目ID (当unique_key未提供时使用)
        in: query
        name: id
        type: string
      - description: Cookie信息
        in: formData
        name: cookie
        type: string
      - description: 位置信息
        in: formData
        name: location
        type: string
      - description: 顶级位置
        in: formData
        name: toplocation
        type: string
      - description: 读取URL
        in: formData
        name: duquurl
        type: string
      - description: JSONP回调函数名
        in: query
        name: callback
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 数据提交成功
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  properties:
                    hashid:
                      type: string
                  type: object
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 项目未找到
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      summary: 提交数据
      tags:
      - Gateway
    post:
      consumes:
      - multipart/form-data
      - application/json
      - application/x-www-form-urlencoded
      description: 接收并存储项目相关数据
      parameters:
      - description: 项目唯一键
        in: query
        name: unique_key
        type: string
      - description: 项目ID (当unique_key未提供时使用)
        in: query
        name: id
        type: string
      - description: Cookie信息
        in: formData
        name: cookie
        type: string
      - description: 位置信息
        in: formData
        name: location
        type: string
      - description: 顶级位置
        in: formData
        name: toplocation
        type: string
      - description: 读取URL
        in: formData
        name: duquurl
        type: string
      - description: JSONP回调函数名
        in: query
        name: callback
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 数据提交成功
          schema:
            allOf:
            - $ref: '#/definitions/dto.StandardResponse'
            - properties:
                data:
                  properties:
                    hashid:
                      type: string
                  type: object
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "404":
          description: 项目未找到
          schema:
            $ref: '#/definitions/dto.StandardResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/dto.StandardResponse'
      summary: 提交数据
      tags:
      - Gateway
schemes:
- http
- https
securityDefinitions:
  BearerAuth:
    description: 请输入 'Bearer {token}' - 注意Bearer和token之间有一个空格
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
