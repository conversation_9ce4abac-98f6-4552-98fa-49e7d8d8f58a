package dto

// StandardResponse 标准响应结构
// 用于统一API响应格式
type StandardResponse struct {
	Code    int         `json:"code"`    // 业务状态码，0表示成功
	Message string      `json:"message"` // 响应消息
	Data    interface{} `json:"data"`    // 响应数据
	Error   string      `json:"error,omitempty"` // 错误信息，仅在开发环境显示
}

// PaginatedResponse 分页响应结构
// 用于列表数据的分页返回
type PaginatedResponse struct {
	Items      interface{} `json:"items"`       // 当前页数据项
	Total      int64       `json:"total"`       // 总记录数
	Page       int         `json:"page"`        // 当前页码
	PageSize   int         `json:"page_size"`   // 每页大小
	TotalPages int         `json:"total_pages"` // 总页数
}

// BatchIDsDTO 批量操作ID列表
// 用于批量删除、批量更新等操作
type BatchIDsDTO struct {
	IDs []uint64 `json:"ids" validate:"required,min=1"` // ID列表
}

// UserResponse 用户基本信息响应
// 用于返回用户的基本信息
type UserResponse struct {
	ID       uint64 `json:"id"`                // 用户ID
	Username string `json:"username"`          // 用户名
	Name     string `json:"name,omitempty"`    // 姓名
	Avatar   string `json:"avatar,omitempty"`  // 头像
	Email    string `json:"email,omitempty"`   // 邮箱
} 