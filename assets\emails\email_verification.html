<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮箱验证</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            border-radius: 8px;
            padding: 30px;
            background-color: #ffffff;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            padding-bottom: 15px;
            border-bottom: 1px solid #eaeaea;
            margin-bottom: 25px;
        }

        .header h2 {
            color: #3a3a3a;
            margin: 0;
            font-weight: 600;
        }

        .logo {
            text-align: center;
            margin-bottom: 20px;
        }

        .content {
            color: #555;
            font-size: 15px;
        }

        .verify-button {
            display: inline-block;
            background-color: #4285f4;
            color: white !important;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 4px;
            margin: 25px 0;
            font-weight: 500;
            transition: background-color 0.3s ease;
            text-align: center;
        }

        .verify-button:hover {
            background-color: #3367d6;
        }

        .link-block {
            background-color: #f9f9f9;
            border: 1px solid #eaeaea;
            border-radius: 4px;
            padding: 12px;
            margin: 15px 0;
            word-break: break-all;
            font-size: 14px;
        }

        .footer {
            margin-top: 30px;
            font-size: 13px;
            text-align: center;
            color: #888;
            padding-top: 15px;
            border-top: 1px solid #eaeaea;
        }

        p {
            margin: 16px 0;
        }

        @media only screen and (max-width: 600px) {
            body {
                padding: 10px;
            }
            
            .container {
                padding: 20px;
            }

            .verify-button {
                display: block;
                text-align: center;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h2>邮箱验证</h2>
        </div>
        <div class="content">
            <p>尊敬的 <strong>{{.Username}}</strong>：</p>
            <p>感谢您注册我们的服务。请点击下面的按钮验证您的邮箱地址：</p>
            <p style="text-align: center;">
                <a href="{{.VerificationLink}}" class="verify-button">验证邮箱</a>
            </p>
            <p>如果按钮无法正常工作，请复制以下链接到浏览器地址栏：</p>
            <div class="link-block">{{.VerificationLink}}</div>
            <p>此链接将在 <strong>{{.ExpireMinutes}} 分钟</strong> 后失效。如果您没有注册我们的服务，请忽略此邮件。</p>
        </div>
        <div class="footer">
            <p>此邮件由系统自动发送，请勿回复。</p>
            <p>© 2025 XSS测试平台 | 安全中心</p>
        </div>
    </div>
</body>

</html> 