package queue

import (
	"go-fiber-api/utils/queue/tasks"
	"log"
	"time"

	"github.com/hibiken/asynq"
)

// EnqueueArticlePublishTask 入队公告发布任务
func EnqueueArticlePublishTask(articleID uint64, title, content, author string, delay time.Duration) error {
	client := GetClient()
	// 不再需要defer client.Close()，因为是单例

	task, err := tasks.NewArticlePublishTask(articleID, title, content, author)
	if err != nil {
		return err
	}

	var info *asynq.TaskInfo
	if delay > 0 {
		// 设置延迟发布
		info, err = client.EnqueueWithOptions(task, asynq.ProcessIn(delay))
	} else {
		// 立即发布
		info, err = client.Enqueue(task)
	}

	if err != nil {
		return err
	}

	log.Printf("公告发布任务入队成功: ID=%s Queue=%s", info.ID, info.Queue)
	return nil
}
