<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮箱验证成功</title>
    <style>
        :root {
            --primary-color: #4285f4;
            --primary-dark: #3367d6;
            --success-color: #4caf50;
            --border-color: #e0e0e0;
            --text-color: #333;
            --text-secondary: #666;
            --bg-color: #f5f7fa;
            --card-bg: #fff;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--bg-color);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            width: 100%;
            max-width: 450px;
            border-radius: 10px;
            padding: 30px;
            background-color: var(--card-bg);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            text-align: center;
        }

        .header {
            margin-bottom: 30px;
        }

        h2 {
            color: var(--text-color);
            font-weight: 600;
            font-size: 26px;
            margin-bottom: 10px;
        }

        .subtitle {
            color: var(--text-secondary);
            font-size: 16px;
            margin-bottom: 20px;
        }

        .success-icon {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 80px;
            height: 80px;
            margin: 0 auto 25px;
            border-radius: 50%;
            background-color: rgba(76, 175, 80, 0.1);
            color: var(--success-color);
            font-size: 40px;
        }

        .message {
            margin: 25px 0;
            font-size: 16px;
            color: var(--text-secondary);
        }

        .login-button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            transition: background-color 0.3s ease, transform 0.1s ease;
        }

        .login-button:hover {
            background-color: var(--primary-dark);
        }
        
        .login-button:active {
            transform: translateY(1px);
        }

        @media only screen and (max-width: 480px) {
            .container {
                padding: 20px;
            }
            
            h2 {
                font-size: 22px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="success-icon">✓</div>
        <div class="header">
            <h2>邮箱验证成功</h2>
            <div class="subtitle">您的邮箱已成功验证</div>
        </div>
        
        <div class="message">
            <p>感谢您验证您的邮箱地址。您现在可以使用所有平台功能了。</p>
        </div>
        
        <a href="/api/admin/auth/login" class="login-button">前往登录</a>
    </div>

    <script>
        // 5秒后自动跳转到登录页面
        setTimeout(function() {
            window.location.href = "/api/admin/auth/login";
        }, 5000);
    </script>
</body>

</html> 