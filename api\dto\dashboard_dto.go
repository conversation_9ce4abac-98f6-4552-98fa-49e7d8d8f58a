package dto

// DashboardDataResponse 控制面板数据响应
type DashboardDataResponse struct {
	UserCount         int64 `json:"user_count"`          // 用户总数
	RoleCount         int64 `json:"role_count"`          // 角色总数
	PermissionCount   int64 `json:"permission_count"`    // 权限总数
	ProjectCount      int64 `json:"project_count"`       // 项目总数
	MenuCount         int64 `json:"menu_count"`          // 菜单总数
	TodayUserCount    int64 `json:"today_user_count"`    // 今日新增用户数
	TodayProjectCount int64 `json:"today_project_count"` // 今日新增项目数
}

// OperationLogResponse 操作日志响应
type OperationLogResponse struct {
	ID        uint64 `json:"id"`         // 日志ID
	UserID    uint64 `json:"user_id"`    // 用户ID
	Username  string `json:"username"`   // 用户名
	Path      string `json:"path"`       // 操作路径
	Method    string `json:"method"`     // HTTP方法
	IP        string `json:"ip"`         // IP地址
	Input     string `json:"input"`      // 输入数据
	CreatedAt string `json:"created_at"` // 创建时间
}

// SystemInfoResponse 系统信息响应
type SystemInfoResponse struct {
	Version         string `json:"version"`          // 系统版本
	Uptime          string `json:"uptime"`           // 运行时间
	GoVersion       string `json:"go_version"`       // Go版本
	ServerTime      string `json:"server_time"`      // 服务器时间
	DatabaseVersion string `json:"database_version"` // 数据库版本
}

// LogStatisticsResponse 日志统计响应
type LogStatisticsResponse struct {
	Date  string `json:"date"`  // 日期
	Count int64  `json:"count"` // 操作数量
}

// UserStatisticsResponse 用户统计响应
type UserStatisticsResponse struct {
	Date  string `json:"date"`  // 日期
	Count int64  `json:"count"` // 用户数量
} 