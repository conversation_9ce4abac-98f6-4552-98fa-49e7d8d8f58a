package handlers

import (
	"go-fiber-api/api/dto"
	"go-fiber-api/database"
	"go-fiber-api/models"
	"go-fiber-api/utils"
	"strconv"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

// CreateXssTemplate 创建XSS模板
// @Summary 创建XSS模板
// @Description 创建新的XSS模板
// @Tags XSS模板管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body dto.CreateXssTemplateRequest true "创建XSS模板请求"
// @Success 200 {object} dto.StandardResponse{data=dto.XssTemplateResponse} "创建成功"
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 401 {object} dto.StandardResponse "未授权"
// @Failure 500 {object} dto.StandardResponse "服务器错误"
// @Router /admin/xss-templates [post]
func CreateXssTemplate(c *fiber.Ctx) error {
	var req dto.CreateXssTemplateRequest
	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "请求参数格式错误", err)
	}

	// 参数验证
	if err := utils.ValidateStruct(&req); err != nil {
		return utils.BadRequest(c, "参数验证失败", err)
	}

	// 检查项目是否存在
	var project models.Project
	if err := database.DB.First(&project, req.ProjectID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "项目不存在")
		}
		return utils.ServerError(c, "查询项目失败", err)
	}

	// 创建XSS模板
	xssTemplate := models.XssTemplate{
		ProjectID: req.ProjectID,
		Title:     req.Title,
		Content:   req.Content,
	}

	if err := database.DB.Create(&xssTemplate).Error; err != nil {
		return utils.ServerError(c, "创建XSS模板失败", err)
	}

	// 转换为响应格式
	response := dto.XssTemplateResponse{
		ID:        xssTemplate.ID,
		ProjectID: xssTemplate.ProjectID,
		Title:     xssTemplate.Title,
		Content:   xssTemplate.Content,
		CreatedAt: xssTemplate.CreatedAt,
		UpdatedAt: xssTemplate.UpdatedAt,
	}

	return utils.Success(c, "创建XSS模板成功", response)
}

// GetXssTemplates 获取XSS模板列表
// @Summary 获取XSS模板列表
// @Description 获取XSS模板列表，支持分页和筛选
// @Tags XSS模板管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param project_id query int false "项目ID"
// @Param title query string false "模板标题关键词"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Success 200 {object} dto.StandardResponse{data=dto.XssTemplateListResponse} "获取成功"
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 401 {object} dto.StandardResponse "未授权"
// @Failure 500 {object} dto.StandardResponse "服务器错误"
// @Router /admin/xss-templates [get]
func GetXssTemplates(c *fiber.Ctx) error {
	var req dto.XssTemplateListRequest

	// 解析查询参数
	if err := c.QueryParser(&req); err != nil {
		return utils.BadRequest(c, "查询参数格式错误", err)
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	// 构建查询
	query := database.DB.Model(&models.XssTemplate{})

	// 按项目ID筛选
	if req.ProjectID != nil {
		query = query.Where("project_id = ?", *req.ProjectID)
	}

	// 按标题搜索
	if req.Title != "" {
		query = query.Where("title LIKE ?", "%"+req.Title+"%")
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return utils.ServerError(c, "查询总数失败", err)
	}

	// 分页查询
	var xssTemplates []models.XssTemplate
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&xssTemplates).Error; err != nil {
		return utils.ServerError(c, "查询XSS模板列表失败", err)
	}

	// 转换为响应格式
	var templateList []dto.XssTemplateResponse
	for _, template := range xssTemplates {
		templateList = append(templateList, dto.XssTemplateResponse{
			ID:        template.ID,
			ProjectID: template.ProjectID,
			Title:     template.Title,
			Content:   template.Content,
			CreatedAt: template.CreatedAt,
			UpdatedAt: template.UpdatedAt,
		})
	}

	response := dto.XssTemplateListResponse{
		List:     templateList,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	return utils.Success(c, "获取XSS模板列表成功", response)
}

// GetXssTemplate 获取单个XSS模板详情
// @Summary 获取XSS模板详情
// @Description 根据ID获取XSS模板详情
// @Tags XSS模板管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "模板ID"
// @Success 200 {object} dto.StandardResponse{data=dto.XssTemplateResponse} "获取成功"
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 401 {object} dto.StandardResponse "未授权"
// @Failure 404 {object} dto.StandardResponse "模板不存在"
// @Failure 500 {object} dto.StandardResponse "服务器错误"
// @Router /admin/xss-templates/{id} [get]
func GetXssTemplate(c *fiber.Ctx) error {
	// 获取模板ID
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的模板ID", err)
	}

	// 查询模板
	var xssTemplate models.XssTemplate
	if err := database.DB.First(&xssTemplate, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "XSS模板不存在")
		}
		return utils.ServerError(c, "查询XSS模板失败", err)
	}

	// 转换为响应格式
	response := dto.XssTemplateResponse{
		ID:        xssTemplate.ID,
		ProjectID: xssTemplate.ProjectID,
		Title:     xssTemplate.Title,
		Content:   xssTemplate.Content,
		CreatedAt: xssTemplate.CreatedAt,
		UpdatedAt: xssTemplate.UpdatedAt,
	}

	return utils.Success(c, "获取XSS模板详情成功", response)
}

// UpdateXssTemplate 更新XSS模板
// @Summary 更新XSS模板
// @Description 根据ID更新XSS模板信息
// @Tags XSS模板管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "模板ID"
// @Param request body dto.UpdateXssTemplateRequest true "更新XSS模板请求"
// @Success 200 {object} dto.StandardResponse{data=dto.XssTemplateResponse} "更新成功"
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 401 {object} dto.StandardResponse "未授权"
// @Failure 404 {object} dto.StandardResponse "模板不存在"
// @Failure 500 {object} dto.StandardResponse "服务器错误"
// @Router /admin/xss-templates/{id} [put]
func UpdateXssTemplate(c *fiber.Ctx) error {
	// 获取模板ID
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的模板ID", err)
	}

	var req dto.UpdateXssTemplateRequest
	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "请求参数格式错误", err)
	}

	// 参数验证
	if err := utils.ValidateStruct(&req); err != nil {
		return utils.BadRequest(c, "参数验证失败", err)
	}

	// 查询模板是否存在
	var xssTemplate models.XssTemplate
	if err := database.DB.First(&xssTemplate, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "XSS模板不存在")
		}
		return utils.ServerError(c, "查询XSS模板失败", err)
	}

	// 更新字段
	updates := make(map[string]interface{})
	if req.Title != nil {
		updates["title"] = *req.Title
	}
	if req.Content != nil {
		updates["content"] = *req.Content
	}

	// 如果没有要更新的字段
	if len(updates) == 0 {
		return utils.BadRequest(c, "没有要更新的字段", nil)
	}

	// 执行更新
	if err := database.DB.Model(&xssTemplate).Updates(updates).Error; err != nil {
		return utils.ServerError(c, "更新XSS模板失败", err)
	}

	// 重新查询更新后的数据
	if err := database.DB.First(&xssTemplate, id).Error; err != nil {
		return utils.ServerError(c, "查询更新后的XSS模板失败", err)
	}

	// 转换为响应格式
	response := dto.XssTemplateResponse{
		ID:        xssTemplate.ID,
		ProjectID: xssTemplate.ProjectID,
		Title:     xssTemplate.Title,
		Content:   xssTemplate.Content,
		CreatedAt: xssTemplate.CreatedAt,
		UpdatedAt: xssTemplate.UpdatedAt,
	}

	return utils.Success(c, "更新XSS模板成功", response)
}

// DeleteXssTemplate 删除XSS模板
// @Summary 删除XSS模板
// @Description 根据ID软删除XSS模板
// @Tags XSS模板管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "模板ID"
// @Success 200 {object} dto.StandardResponse "删除成功"
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 401 {object} dto.StandardResponse "未授权"
// @Failure 404 {object} dto.StandardResponse "模板不存在"
// @Failure 500 {object} dto.StandardResponse "服务器错误"
// @Router /admin/xss-templates/{id} [delete]
func DeleteXssTemplate(c *fiber.Ctx) error {
	// 获取模板ID
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的模板ID", err)
	}

	// 查询模板是否存在
	var xssTemplate models.XssTemplate
	if err := database.DB.First(&xssTemplate, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "XSS模板不存在")
		}
		return utils.ServerError(c, "查询XSS模板失败", err)
	}

	// 软删除模板
	if err := database.DB.Delete(&xssTemplate).Error; err != nil {
		return utils.ServerError(c, "删除XSS模板失败", err)
	}

	return utils.Success(c, "删除XSS模板成功", nil)
}
