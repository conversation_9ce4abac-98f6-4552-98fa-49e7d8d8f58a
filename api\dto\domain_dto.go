package dto

// DomainListItem 域名列表项
type DomainListItem struct {
	ID        uint64 `json:"id"`         // 域名ID
	Domain    string `json:"domain"`     // 域名
	Type      int8   `json:"type"`       // 类型 10 过滤域名/20 切换使用的域名
	State     int    `json:"state"`      // 状态
	CreatedAt string `json:"created_at"` // 创建时间
}

// DomainResponse 域名详情
type DomainResponse struct {
	ID        uint64 `json:"id"`         // 域名ID
	Domain    string `json:"domain"`     // 域名
	Type      int8   `json:"type"`       // 类型 10 过滤域名/20 切换使用的域名
	State     int    `json:"state"`      // 状态
	CreatedAt string `json:"created_at"` // 创建时间
	UpdatedAt string `json:"updated_at"` // 更新时间
}

// DomainRequest 域名创建请求
type DomainRequest struct {
	Domain string `json:"domain" validate:"required"` // 域名
	Type   int8   `json:"type" validate:"required,oneof=10 20"` // 类型 10 过滤域名/20 切换使用的域名
	State  int    `json:"state" validate:"required,min=0,max=1"` // 状态
}

// DomainUpdateRequest 域名更新请求
type DomainUpdateRequest struct {
	Domain *string `json:"domain"` // 域名
	Type   *int8   `json:"type" validate:"omitempty,oneof=10 20"` // 类型 10 过滤域名/20 切换使用的域名
	State  *int    `json:"state" validate:"omitempty,min=0,max=1"` // 状态
} 