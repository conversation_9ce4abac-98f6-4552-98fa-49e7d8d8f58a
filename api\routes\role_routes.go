package routes

import (
	"go-fiber-api/api/handlers"
	"go-fiber-api/api/middleware"
	"go-fiber-api/utils"

	"github.com/gofiber/fiber/v2"
)

// SetupRoleRoutes 设置角色管理相关路由
func SetupRoleRoutes(router fiber.Router) {
	// 角色路由组
	roles := router.Group("/role")
	
	// 添加认证中间件
	roles.Use(utils.RequireAuthentication)

	// 角色CRUD路由
	roles.Get("/index", middleware.RequirePermission("roles.view"), handlers.GetRoles)           // 获取角色列表
	roles.Get("/all", middleware.RequirePermission("roles.view"), handlers.GetAllRoles)           // 获取所有角色(不分页)
	roles.Get("/index/:id", middleware.RequirePermission("roles.view"), handlers.GetRole)        // 获取单个角色
	roles.Post("/index", middleware.RequirePermission("roles.create"), handlers.CreateRole)        // 创建角色
	roles.Put("/index/:id", middleware.RequirePermission("roles.edit"), handlers.UpdateRole)     // 更新角色
	roles.Delete("/index/:id", middleware.RequirePermission("roles.delete"), handlers.DeleteRole)  // 删除角色
	
	// 批量删除角色
	roles.Post("/batch-delete", middleware.RequirePermission("roles.delete"), handlers.BatchDeleteRoles)
	
	// 角色权限分配
	roles.Post("/:id/permissions", middleware.RequirePermission("roles.edit"), handlers.AssignPermissions) // 分配权限给角色
	
	// 角色菜单分配
	roles.Post("/:id/menus", middleware.RequirePermission("roles.edit"), handlers.AssignMenus) // 分配菜单给角色
	
	// 角色用户管理
	roles.Get("/:id/users", middleware.RequirePermission("roles.view"), handlers.GetRoleUsers) // 获取拥有角色的用户
	roles.Post("/:id/users", middleware.RequirePermission("roles.edit"), handlers.AssignRoleToUsers) // 分配角色给用户
	roles.Post("/:id/users/remove", middleware.RequirePermission("roles.edit"), handlers.RemoveRoleFromUsers) // 移除用户的角色

	// 权限继承同步
	roles.Post("/sync-permissions", middleware.RequirePermission("roles.edit"), handlers.SyncAllRolePermissions) // 同步所有角色的权限继承
	roles.Post("/:id/sync-permissions", middleware.RequirePermission("roles.edit"), handlers.SyncRolePermissions) // 同步指定角色的权限继承
}