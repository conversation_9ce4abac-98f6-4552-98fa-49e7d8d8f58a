package handlers

import (
	"fmt"
	"go-fiber-api/api/dto"
	"go-fiber-api/database"
	"go-fiber-api/models"
	"go-fiber-api/utils"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

// GetRoles 获取角色列表
// @Summary 获取角色列表
// @Description 获取角色列表，支持分页和筛选
// @Tags 角色管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码，默认为1"
// @Param page_size query int false "每页条数，默认为10"
// @Param name query string false "角色名称(模糊搜索)"
// @Param guard_name query string false "Guard名称"
// @Success 200 {object} dto.StandardResponse{data=dto.PaginatedResponse{items=[]dto.RoleResponse}}
// @Router /admin/role/index [get]
func GetRoles(c *fiber.Ctx) error {
	// 获取分页参数
	page, _ := strconv.Atoi(c.Query("page", "1"))
	pageSize, _ := strconv.Atoi(c.Query("page_size", "10"))

	// 获取筛选参数
	name := c.Query("name", "")
	guardName := c.Query("guard_name", "")

	// 构建查询
	query := database.DB.Model(&models.Role{})

	// 应用筛选条件
	if name != "" {
		query = query.Where("name LIKE ?", "%"+name+"%")
	}

	if guardName != "" {
		query = query.Where("guard_name = ?", guardName)
	}

	// 计算总数
	var total int64
	query.Count(&total)

	// 获取分页数据
	var roles []models.Role
	query.Order("id DESC").
		Offset((page - 1) * pageSize).
		Limit(pageSize).
		Find(&roles)

	// 获取每个角色的权限数量
	roleIDs := make([]uint64, len(roles))
	for i, role := range roles {
		roleIDs[i] = role.ID
	}
	
	// 权限计数映射
	permissionCounts := make(map[uint64]int64)
	if len(roleIDs) > 0 {
		rows, err := database.DB.Raw("SELECT role_id, COUNT(*) as count FROM admin_role_permissions WHERE role_id IN ? GROUP BY role_id", roleIDs).Rows()
		if err == nil {
			defer rows.Close()
			for rows.Next() {
				var roleID uint64
				var count int64
				rows.Scan(&roleID, &count)
				permissionCounts[roleID] = count
			}
		}
	}

	// 转换为DTO
	var roleDTOs []dto.RoleResponse
	for _, role := range roles {
		roleDTOs = append(roleDTOs, dto.RoleResponse{
			ID:              role.ID,
			Name:            role.Name,
			GuardName:       role.GuardName,
			Remark:          role.Remark,
			PermissionCount: permissionCounts[role.ID],
		})
	}

	// 返回分页响应
	return utils.SuccessPaginated(c, "获取角色列表成功", roleDTOs, total, page, pageSize)
}

// GetAllRoles 获取所有角色(不分页)
// @Summary 获取所有角色
// @Description 获取系统中的所有角色，不分页
// @Tags 角色管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} dto.StandardResponse{data=[]dto.RoleResponse}
// @Router /admin/role/all [get]
func GetAllRoles(c *fiber.Ctx) error {
	// 构建查询
	query := database.DB.Model(&models.Role{})

	// 获取所有角色
	var roles []models.Role
	if err := query.Order("id DESC").Find(&roles).Error; err != nil {
		return utils.ServerError(c, "获取角色列表失败", err)
	}

	// 转换为DTO
	var roleDTOs []dto.RoleResponse
	for _, role := range roles {
		roleDTOs = append(roleDTOs, dto.RoleResponse{
			ID:        role.ID,
			Name:      role.Name,
			GuardName: role.GuardName,
			Remark:    role.Remark,
		})
	}

	return utils.Success(c, "获取所有角色成功", roleDTOs)
}

// GetRole 获取单个角色详情
// @Summary 获取角色详情
// @Description 根据ID获取角色详情，包含权限树形结构
// @Tags 角色管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "角色ID"
// @Success 200 {object} dto.StandardResponse{data=dto.RoleDetailResponse}
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 404 {object} dto.StandardResponse "角色不存在"
// @Router /admin/role/index/{id} [get]
func GetRole(c *fiber.Ctx) error {
	// 获取角色ID
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的角色ID", err)
	}

	// 查询角色
	var role models.Role
	if err := database.DB.Preload("Permissions").Preload("Menus").First(&role, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "角色不存在")
		}
		return utils.ServerError(c, "获取角色失败", err)
	}

	// 转换为DTO
	response := dto.RoleDetailResponse{
		ID:          role.ID,
		Name:        role.Name,
		GuardName:   role.GuardName,
		Remark:      role.Remark,
		Permissions: []dto.RolePermissionResponse{},
		Menus:       []dto.RoleMenuResponse{},
	}

	// 处理权限，转换为响应DTO
	var permissionResponses []dto.RolePermissionResponse
	for _, perm := range role.Permissions {
		permResponse := dto.RolePermissionResponse{
			ID:         perm.ID,
			Name:       perm.Name,
			Slug:       perm.Slug,
			HttpMethod: perm.HttpMethod,
			HttpPath:   perm.HttpPath,
			Order:      perm.Order,
			ParentID:   perm.ParentID,
		}
		permissionResponses = append(permissionResponses, permResponse)
	}
	
	// 构建权限树
	response.Permissions = buildRolePermissionTree(permissionResponses, 0)

	// 处理菜单
	for _, menu := range role.Menus {
		menuResponse := dto.RoleMenuResponse{
			ID:       menu.ID,
			ParentID: menu.ParentID,
			Order:    menu.Order,
			Title:    menu.Title,
			Icon:     menu.Icon,
			URI:      menu.URI,
		}
		response.Menus = append(response.Menus, menuResponse)
	}

	// 检查是否为超级管理员
	isAdmin := false
	for _, roleName := range []string{"administrator", "admin"} {
		if role.Name == roleName {
			isAdmin = true
			break
		}
	}
	response.IsAdmin = isAdmin

	return utils.Success(c, "获取角色详情成功", response)
}

// CreateRole 创建角色
// @Summary 创建角色
// @Description 创建新的角色
// @Tags 角色管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param role body dto.RoleCreateDTO true "角色信息"
// @Success 200 {object} dto.StandardResponse{data=dto.RoleResponse}
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Router /admin/role/index [post]
func CreateRole(c *fiber.Ctx) error {
	// 解析请求体
	var req dto.RoleCreateDTO
	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}

	// 验证请求数据
	if err := utils.ValidateStruct(req); err != nil {
		return utils.BadRequest(c, "数据验证失败", err)
	}

	// 创建新角色
	slug := generateSlug(req.Name)
	fmt.Printf("生成角色slug: %s, 来源名称: %s\n", slug, req.Name)
	
	role := models.Role{
		Name:      req.Name,
		Slug:      slug,
		GuardName: req.GuardName,
		Remark:    req.Remark,
	}

	err := database.DB.Transaction(func(tx *gorm.DB) error {
		// 创建角色
		if err := tx.Create(&role).Error; err != nil {
			return err
		}

		// 处理权限关联
		if len(req.PermissionIDs) > 0 {
			// 获取包含子权限的完整权限列表
			allPermissionIDs, err := utils.GetPermissionsWithChildren(req.PermissionIDs)
			if err != nil {
				return err
			}

			// 查找所有权限（包括子权限）
			var permissions []models.Permission
			if err := tx.Where("id IN ?", allPermissionIDs).Find(&permissions).Error; err != nil {
				return err
			}
			if err := tx.Model(&role).Association("Permissions").Replace(permissions); err != nil {
				return err
			}
		}

		// 处理菜单关联
		if len(req.MenuIDs) > 0 {
			var menus []models.Menu
			if err := tx.Where("id IN ?", req.MenuIDs).Find(&menus).Error; err != nil {
				return err
			}
			if err := tx.Model(&role).Association("Menus").Replace(menus); err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return utils.ServerError(c, "创建角色失败", err)
	}

	// 转换为DTO
	response := dto.RoleResponse{
		ID:        role.ID,
		Name:      role.Name,
		GuardName: role.GuardName,
		Remark:    role.Remark,
	}

	return utils.Success(c, "创建角色成功", response)
}

// UpdateRole 更新角色
// @Summary 更新角色
// @Description 更新现有角色
// @Tags 角色管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "角色ID"
// @Param role body dto.RoleUpdateDTO true "角色更新信息"
// @Success 200 {object} dto.StandardResponse{data=dto.RoleResponse}
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 404 {object} dto.StandardResponse "角色不存在"
// @Router /admin/role/index/{id} [put]
func UpdateRole(c *fiber.Ctx) error {
	// 获取角色ID
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的角色ID", err)
	}

	// 查询角色
	var role models.Role
	if err := database.DB.First(&role, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "角色不存在")
		}
		return utils.ServerError(c, "获取角色失败", err)
	}

	// 解析请求体
	var req dto.RoleUpdateDTO
	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}

	// 验证请求数据
	if err := utils.ValidateStruct(req); err != nil {
		return utils.BadRequest(c, "数据验证失败", err)
	}

	// 准备更新数据
	updates := make(map[string]interface{})

	if req.Name != nil {
		updates["name"] = *req.Name
		// 当名称更新时也更新slug
		updates["slug"] = generateSlug(*req.Name)
	}

	if req.GuardName != nil {
		updates["guard_name"] = *req.GuardName
	}

	if req.Remark != nil {
		updates["remark"] = *req.Remark
	}

	err = database.DB.Transaction(func(tx *gorm.DB) error {
		// 更新角色基本信息
		if len(updates) > 0 {
			if err := tx.Model(&role).Updates(updates).Error; err != nil {
				return err
			}
		}

		// 处理权限关联
		if req.PermissionIDs != nil {
			// 获取包含子权限的完整权限列表
			allPermissionIDs, err := utils.GetPermissionsWithChildren(req.PermissionIDs)
			if err != nil {
				return err
			}

			// 查找所有权限（包括子权限）
			var permissions []models.Permission
			if err := tx.Where("id IN ?", allPermissionIDs).Find(&permissions).Error; err != nil {
				return err
			}
			if err := tx.Model(&role).Association("Permissions").Replace(permissions); err != nil {
				return err
			}
		}

		// 处理菜单关联
		if req.MenuIDs != nil {
			var menus []models.Menu
			if err := tx.Where("id IN ?", req.MenuIDs).Find(&menus).Error; err != nil {
				return err
			}
			if err := tx.Model(&role).Association("Menus").Replace(menus); err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return utils.ServerError(c, "更新角色失败", err)
	}

	// 重新获取更新后的角色
	if err := database.DB.First(&role, id).Error; err != nil {
		return utils.ServerError(c, "获取更新后的角色失败", err)
	}

	// 转换为DTO
	response := dto.RoleResponse{
		ID:        role.ID,
		Name:      role.Name,
		GuardName: role.GuardName,
		Remark:    role.Remark,
	}

	return utils.Success(c, "更新角色成功", response)
}

// DeleteRole 删除角色
// @Summary 删除角色
// @Description 删除现有角色
// @Tags 角色管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "角色ID"
// @Success 200 {object} dto.StandardResponse "删除成功"
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 404 {object} dto.StandardResponse "角色不存在"
// @Router /admin/role/index/{id} [delete]
func DeleteRole(c *fiber.Ctx) error {
	// 获取角色ID
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的角色ID", err)
	}

	// 查询角色
	var role models.Role
	if err := database.DB.First(&role, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "角色不存在")
		}
		return utils.ServerError(c, "获取角色失败", err)
	}

	// 不允许删除管理员角色
	if role.Name == "administrator" {
		return utils.BadRequest(c, "不能删除管理员角色", nil)
	}

	err = database.DB.Transaction(func(tx *gorm.DB) error {
		// 清空关联
		if err := tx.Model(&role).Association("Permissions").Clear(); err != nil {
			return err
		}
		if err := tx.Model(&role).Association("Menus").Clear(); err != nil {
			return err
		}
		if err := tx.Model(&role).Association("Users").Clear(); err != nil {
			return err
		}

		// 删除角色
		if err := tx.Delete(&role).Error; err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return utils.ServerError(c, "删除角色失败", err)
	}

	return utils.Success(c, "删除角色成功", nil)
}

// BatchDeleteRoles 批量删除角色
// @Summary 批量删除角色
// @Description 批量删除指定ID的角色(管理员角色除外)
// @Tags 角色管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param ids body dto.BatchIDsDTO true "角色ID列表"
// @Success 200 {object} dto.StandardResponse "删除成功"
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Router /admin/role/batch-delete [post]
func BatchDeleteRoles(c *fiber.Ctx) error {
	// 解析请求体
	var req dto.BatchIDsDTO
	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}

	// 验证请求数据
	if err := utils.ValidateStruct(req); err != nil {
		return utils.BadRequest(c, "数据验证失败", err)
	}

	if len(req.IDs) == 0 {
		return utils.BadRequest(c, "未指定要删除的角色", nil)
	}

	// 检查是否包含管理员角色
	var adminRole models.Role
	err := database.DB.Where("name = ?", "administrator").First(&adminRole).Error
	if err == nil && utils.ContainsUint64(req.IDs, adminRole.ID) {
		return utils.BadRequest(c, "不能删除管理员角色", nil)
	}

	err = database.DB.Transaction(func(tx *gorm.DB) error {
		// 获取要删除的角色
		var roles []models.Role
		if err := tx.Where("id IN ? AND name != ?", req.IDs, "administrator").Find(&roles).Error; err != nil {
			return err
		}

		// 清空关联
		for _, role := range roles {
			if err := tx.Model(&role).Association("Permissions").Clear(); err != nil {
				return err
			}
			if err := tx.Model(&role).Association("Menus").Clear(); err != nil {
				return err
			}
			if err := tx.Model(&role).Association("Users").Clear(); err != nil {
				return err
			}
		}

		// 删除角色
		if err := tx.Where("id IN ? AND name != ?", req.IDs, "administrator").Delete(&models.Role{}).Error; err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return utils.ServerError(c, "批量删除角色失败", err)
	}

	return utils.Success(c, "批量删除角色成功", nil)
}

// AssignPermissions 分配权限给角色
// @Summary 分配权限给角色
// @Description 批量分配权限给指定角色
// @Tags 角色管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "角色ID"
// @Param permission_ids body dto.BatchIDsDTO true "权限ID列表"
// @Success 200 {object} dto.StandardResponse "分配成功"
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 404 {object} dto.StandardResponse "角色不存在"
// @Router /admin/role/{id}/permissions [post]
func AssignPermissions(c *fiber.Ctx) error {
	// 获取角色ID
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的角色ID", err)
	}

	// 查询角色
	var role models.Role
	if err := database.DB.First(&role, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "角色不存在")
		}
		return utils.ServerError(c, "获取角色失败", err)
	}

	// 解析请求体
	var req dto.BatchIDsDTO
	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}

	// 验证请求数据
	if err := utils.ValidateStruct(req); err != nil {
		return utils.BadRequest(c, "数据验证失败", err)
	}

	err = database.DB.Transaction(func(tx *gorm.DB) error {
		// 获取包含子权限的完整权限列表
		allPermissionIDs, err := utils.GetPermissionsWithChildren(req.IDs)
		if err != nil {
			return err
		}

		// 查找所有权限（包括子权限）
		var permissions []models.Permission
		if err := tx.Where("id IN ?", allPermissionIDs).Find(&permissions).Error; err != nil {
			return err
		}

		// 替换角色的权限
		if err := tx.Model(&role).Association("Permissions").Replace(permissions); err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return utils.ServerError(c, "分配权限失败", err)
	}

	return utils.Success(c, "分配权限成功", nil)
}

// AssignMenus 分配菜单给角色
// @Summary 分配菜单给角色
// @Description 批量分配菜单给指定角色
// @Tags 角色管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "角色ID"
// @Param menu_ids body dto.BatchIDsDTO true "菜单ID列表"
// @Success 200 {object} dto.StandardResponse "分配成功"
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 404 {object} dto.StandardResponse "角色不存在"
// @Router /admin/role/{id}/menus [post]
func AssignMenus(c *fiber.Ctx) error {
	// 获取角色ID
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的角色ID", err)
	}

	// 查询角色
	var role models.Role
	if err := database.DB.First(&role, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "角色不存在")
		}
		return utils.ServerError(c, "获取角色失败", err)
	}

	// 解析请求体
	var req dto.BatchIDsDTO
	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}

	// 验证请求数据
	if err := utils.ValidateStruct(req); err != nil {
		return utils.BadRequest(c, "数据验证失败", err)
	}

	err = database.DB.Transaction(func(tx *gorm.DB) error {
		// 查找菜单
		var menus []models.Menu
		if err := tx.Where("id IN ?", req.IDs).Find(&menus).Error; err != nil {
			return err
		}

		// 替换角色的菜单
		if err := tx.Model(&role).Association("Menus").Replace(menus); err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return utils.ServerError(c, "分配菜单失败", err)
	}

	return utils.Success(c, "分配菜单成功", nil)
}

// AssignRoleToUsers 分配角色给多个用户
// @Summary 分配角色给用户
// @Description 将指定角色分配给多个用户
// @Tags 角色管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "角色ID"
// @Param user_ids body dto.BatchIDsDTO true "用户ID列表"
// @Success 200 {object} dto.StandardResponse "分配成功"
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 404 {object} dto.StandardResponse "角色不存在"
// @Router /admin/role/{id}/users [post]
func AssignRoleToUsers(c *fiber.Ctx) error {
	// 获取角色ID
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的角色ID", err)
	}

	// 查询角色
	var role models.Role
	if err := database.DB.First(&role, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "角色不存在")
		}
		return utils.ServerError(c, "获取角色失败", err)
	}

	// 解析请求体
	var req dto.BatchIDsDTO
	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}

	// 验证请求数据
	if err := utils.ValidateStruct(req); err != nil {
		return utils.BadRequest(c, "数据验证失败", err)
	}

	err = database.DB.Transaction(func(tx *gorm.DB) error {
		// 查找用户
		var users []models.AdminUser
		if err := tx.Where("id IN ?", req.IDs).Find(&users).Error; err != nil {
			return err
		}

		// 将角色分配给用户
		for _, user := range users {
			// 先检查用户是否已有此角色
			var count int64
			tx.Raw("SELECT COUNT(*) FROM admin_role_users WHERE role_id = ? AND admin_user_id = ?", id, user.ID).Count(&count)
			if count == 0 {
				// 如果不存在，添加角色
				if err := tx.Exec("INSERT INTO admin_role_users (role_id, admin_user_id, created_at, updated_at) VALUES (?, ?, NOW(), NOW())", id, user.ID).Error; err != nil {
					return err
				}
			}
		}

		return nil
	})

	if err != nil {
		return utils.ServerError(c, "分配角色给用户失败", err)
	}

	return utils.Success(c, "分配角色给用户成功", nil)
}

// RemoveRoleFromUsers 从用户移除角色
// @Summary 从用户移除角色
// @Description 从多个用户移除指定角色
// @Tags 角色管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "角色ID"
// @Param user_ids body dto.BatchIDsDTO true "用户ID列表"
// @Success 200 {object} dto.StandardResponse "移除成功"
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 404 {object} dto.StandardResponse "角色不存在"
// @Router /admin/role/{id}/users/remove [post]
func RemoveRoleFromUsers(c *fiber.Ctx) error {
	// 获取角色ID
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的角色ID", err)
	}

	// 查询角色
	var role models.Role
	if err := database.DB.First(&role, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "角色不存在")
		}
		return utils.ServerError(c, "获取角色失败", err)
	}

	// 解析请求体
	var req dto.BatchIDsDTO
	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}

	// 验证请求数据
	if err := utils.ValidateStruct(req); err != nil {
		return utils.BadRequest(c, "数据验证失败", err)
	}

	// 执行移除操作
	if err := database.DB.Exec("DELETE FROM admin_role_users WHERE role_id = ? AND admin_user_id IN ?", id, req.IDs).Error; err != nil {
		return utils.ServerError(c, "移除角色失败", err)
	}

	return utils.Success(c, "移除角色成功", nil)
}

// GetRoleUsers 获取拥有指定角色的用户列表
// @Summary 获取角色用户列表
// @Description 获取拥有指定角色的用户列表
// @Tags 角色管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "角色ID"
// @Param page query int false "页码，默认为1"
// @Param page_size query int false "每页条数，默认为10"
// @Success 200 {object} dto.StandardResponse{data=dto.PaginatedResponse{items=[]dto.UserResponse}}
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 404 {object} dto.StandardResponse "角色不存在"
// @Router /admin/role/{id}/users [get]
func GetRoleUsers(c *fiber.Ctx) error {
	// 获取角色ID
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的角色ID", err)
	}

	// 查询角色
	var role models.Role
	if err := database.DB.First(&role, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "角色不存在")
		}
		return utils.ServerError(c, "获取角色失败", err)
	}

	// 获取分页参数
	page, _ := strconv.Atoi(c.Query("page", "1"))
	pageSize, _ := strconv.Atoi(c.Query("page_size", "10"))

	// 联表查询用户
	var total int64
	database.DB.Raw("SELECT COUNT(*) FROM admin_users u JOIN admin_role_users ru ON u.id = ru.admin_user_id WHERE ru.role_id = ?", id).Count(&total)

	// 获取用户数据
	var users []models.AdminUser
	database.DB.Raw(`
		SELECT u.* 
		FROM admin_users u 
		JOIN admin_role_users ru ON u.id = ru.admin_user_id 
		WHERE ru.role_id = ? 
		ORDER BY u.id DESC 
		LIMIT ? OFFSET ?
	`, id, pageSize, (page-1)*pageSize).Scan(&users)

	// 转换为DTO
	var userDTOs []dto.UserResponse
	for _, user := range users {
		userDTOs = append(userDTOs, dto.UserResponse{
			ID:       user.ID,
			Username: user.Username,
			Name:     user.Name,
			Avatar:   user.Avatar,
			Email:    user.Email,
		})
	}

	// 返回分页响应
	return utils.SuccessPaginated(c, "获取角色用户列表成功", userDTOs, total, page, pageSize)
}

// SyncAllRolePermissions 同步所有角色的权限继承
// @Summary 同步所有角色的权限继承
// @Description 为所有角色同步权限继承，确保拥有父权限的角色自动获得子权限
// @Tags 角色管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} dto.StandardResponse "同步成功"
// @Router /admin/roles/sync-permissions [post]
func SyncAllRolePermissions(c *fiber.Ctx) error {
	// 获取所有角色
	var roles []models.Role
	if err := database.DB.Find(&roles).Error; err != nil {
		return utils.ServerError(c, "获取角色列表失败", err)
	}

	// 同步每个角色的权限
	var syncedCount int
	var failedRoles []string

	for _, role := range roles {
		if err := utils.SyncRolePermissions(role.ID); err != nil {
			failedRoles = append(failedRoles, role.Name)
		} else {
			syncedCount++
		}
	}

	if len(failedRoles) > 0 {
		return utils.BadRequest(c, fmt.Sprintf("部分角色同步失败: %s", strings.Join(failedRoles, ", ")), nil)
	}

	return utils.Success(c, fmt.Sprintf("成功同步 %d 个角色的权限继承", syncedCount), nil)
}

// SyncRolePermissions 同步指定角色的权限继承
// @Summary 同步指定角色的权限继承
// @Description 为指定角色同步权限继承，确保拥有父权限的角色自动获得子权限
// @Tags 角色管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "角色ID"
// @Success 200 {object} dto.StandardResponse "同步成功"
// @Failure 404 {object} dto.StandardResponse "角色不存在"
// @Router /admin/roles/{id}/sync-permissions [post]
func SyncRolePermissions(c *fiber.Ctx) error {
	// 获取角色ID
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的角色ID", err)
	}

	// 检查角色是否存在
	var role models.Role
	if err := database.DB.First(&role, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "角色不存在")
		}
		return utils.ServerError(c, "获取角色失败", err)
	}

	// 同步权限
	if err := utils.SyncRolePermissions(id); err != nil {
		return utils.ServerError(c, "同步权限失败", err)
	}

	return utils.Success(c, "权限同步成功", nil)
}

// generateSlug 根据角色名称生成唯一的slug标识
// 将角色名称转换为小写，替换空格为短横线，移除特殊字符，并添加时间戳确保唯一性
func generateSlug(name string) string {
	// 转换为小写
	slug := strings.ToLower(name)
	
	// 替换空格为短横线
	slug = strings.ReplaceAll(slug, " ", "-")
	
	// 移除特殊字符，只保留字母、数字和短横线
	reg := regexp.MustCompile("[^a-z0-9-]+")
	slug = reg.ReplaceAllString(slug, "")
	
	// 处理空字符串情况
	if slug == "" {
		slug = "role"
	}
	
	// 添加时间戳确保唯一性
	timestamp := time.Now().UnixNano() / int64(time.Millisecond)
	slug = fmt.Sprintf("%s-%d", slug, timestamp)
	
	return slug
}

// buildRolePermissionTree 构建角色权限树
func buildRolePermissionTree(permissions []dto.RolePermissionResponse, parentID uint64) []dto.RolePermissionResponse {
	var tree []dto.RolePermissionResponse

	for _, perm := range permissions {
		if perm.ParentID == parentID {
			childPermissions := buildRolePermissionTree(permissions, perm.ID)
			perm.Children = childPermissions
			tree = append(tree, perm)
		}
	}

	return tree
}