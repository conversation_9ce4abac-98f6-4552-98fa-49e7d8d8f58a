# 接口文档

## 目录

- [Web接口](#web接口)
  - [Gateway接口](#gateway接口)
- [API接口](#api接口)
- [Admin接口](#admin接口)
  - [认证相关](#认证相关)
  - [项目管理](#项目管理)
  - [公告管理](#公告管理)
  - [检索器管理](#检索器管理)
  - [模块管理](#模块管理)
  - [过滤域管理](#过滤域管理)
  - [项目内容管理](#项目内容管理)
  - [邀请码管理](#邀请码管理)
  - [友情链接管理](#友情链接管理)
  - [网站配置](#网站配置)

## Web接口

### Gateway接口

| 接口路径 | 请求方式 | 功能描述 | 控制器方法 |
| --- | --- | --- | --- |
| `/logs` | GET | 查看日志 | 返回welcome视图 |
| `/submit` | POST | 提交数据 | GatewayController@Submit |
| `/submit` | GET | 提交数据 | GatewayController@Submit |
| `/keepsession` | GET | 保持会话 | GatewayController@Keepsession |
| `/download/{hash}` | GET | 下载文件 | GatewayController@download |
| `/{unique_key}/{rand_key}.jpg` | GET | 获取唯一图片 | GatewayController@unique_pic |
| `/{unique_key}` | GET | 获取唯一内容 | GatewayController@unique |

## API接口

| 接口路径 | 请求方式 | 功能描述 | 控制器方法 |
| --- | --- | --- | --- |
| `/api/User` | GET | 获取用户信息 | 返回当前认证用户 |

## Admin接口

### 认证相关

| 接口路径 | 请求方式 | 功能描述 | 控制器方法 |
| --- | --- | --- | --- |
| `/admin/auth/signup` | GET | 注册页面 | AuthController@getSignup |
| `/admin/auth/signup` | POST | 提交注册 | AuthController@postSignup |
| `/admin/auth/forgot` | GET | 忘记密码页面 | AuthController@getForgot |
| `/admin/auth/forgot` | POST | 提交忘记密码 | AuthController@postForgot |
| `/admin/captcha` | GET | 获取验证码 | 返回验证码 |
| `/admin/friendly-list` | GET | 获取友情链接列表 | 返回友情链接列表 |

### 项目管理

| 接口路径 | 请求方式 | 功能描述 | 控制器方法 |
| --- | --- | --- | --- |
| `/admin/project/my` | GET | 获取项目列表 | ProjectController@index |
| `/admin/project/my` | POST | 保存新项目 | ProjectController@store |
| `/admin/project/my/{id}` | GET | 查看项目详情 | ProjectController@show |
| `/admin/project/my/{id}` | PUT/PATCH | 更新项目 | ProjectController@update |
| `/admin/project/my/{id}` | DELETE | 删除项目 | ProjectController@destroy |
| `/admin/project/my/{userid}/{projectid}` | GET | 获取项目详情 | ProjectsContentController@detail |
| `/admin/project/my/{userid}/{projectid}` | DELETE | 删除项目 | ProjectsContentController@remove |
| `/admin/project/viewcode/{projectid}` | GET | 查看项目代码 | ProjectController@Viewcode |
| `/admin/project/my/preview` | GET | 预览项目 | ProjectController@preview |

### 公告管理

| 接口路径 | 请求方式 | 功能描述 | 控制器方法 |
| --- | --- | --- | --- |
| `/admin/article/index` | GET | 获取公告列表 | ArticleController@index |
| `/admin/article/index/{id}/edit` | GET | 编辑公告页面 | ArticleController@edit |
| `/admin/article/index/{id}` | PUT/PATCH | 更新公告 | ArticleController@update |
| `/admin/article/index` | POST | 保存新公告 | ArticleController@store |


### 模块管理

| 接口路径 | 请求方式 | 功能描述 | 控制器方法 |
| --- | --- | --- | --- |
| `/admin/module/index` | GET | 获取模块列表 | ModuleController@index |
| `/admin/module/index/create` | GET | 创建模块页面 | ModuleController@create |
| `/admin/module/index` | POST | 保存新模块 | ModuleController@store |
| `/admin/module/index/{id}` | GET | 查看模块详情 | ModuleController@show |
| `/admin/module/index/{id}/edit` | GET | 编辑模块页面 | ModuleController@edit |
| `/admin/module/index/{id}` | PUT/PATCH | 更新模块 | ModuleController@update |
| `/admin/module/index/{id}` | DELETE | 删除模块 | ModuleController@destroy |

### 过滤域管理

| 接口路径 | 请求方式 | 功能描述 | 控制器方法 |
| --- | --- | --- | --- |
| `/admin/domain/index` | GET | 获取过滤域列表 | DomainController@index |
| `/admin/domain/index/create` | GET | 创建过滤域页面 | DomainController@create |
| `/admin/domain/index` | POST | 保存新过滤域 | DomainController@store |
| `/admin/domain/index/{id}` | GET | 查看过滤域详情 | DomainController@show |
| `/admin/domain/index/{id}/edit` | GET | 编辑过滤域页面 | DomainController@edit |
| `/admin/domain/index/{id}` | PUT/PATCH | 更新过滤域 | DomainController@update |
| `/admin/domain/index/{id}` | DELETE | 删除过滤域 | DomainController@destroy |

### 项目内容管理

| 接口路径 | 请求方式 | 功能描述 | 控制器方法 |
| --- | --- | --- | --- |
| `/admin/projectscontent/index` | GET | 获取项目内容列表 | ProjectsContentController@index |
| `/admin/projectscontent/index/create` | GET | 创建项目内容页面 | ProjectsContentController@create |
| `/admin/projectscontent/index` | POST | 保存新项目内容 | ProjectsContentController@store |
| `/admin/projectscontent/index/{id}` | GET | 查看项目内容详情 | ProjectsContentController@show |
| `/admin/projectscontent/index/{id}/edit` | GET | 编辑项目内容页面 | ProjectsContentController@edit |
| `/admin/projectscontent/index/{id}` | PUT/PATCH | 更新项目内容 | ProjectsContentController@update |
| `/admin/projectscontent/index/{id}` | DELETE | 删除项目内容 | ProjectsContentController@destroy |

### 邀请码管理

| 接口路径 | 请求方式 | 功能描述 | 控制器方法 |
| --- | --- | --- | --- |
| `/admin/invitation/index` | GET | 获取邀请码列表 | InvitationController@index |
| `/admin/invitation/index/create` | GET | 创建邀请码页面 | InvitationController@create |
| `/admin/invitation/index` | POST | 保存新邀请码 | InvitationController@store |
| `/admin/invitation/index/{id}` | GET | 查看邀请码详情 | InvitationController@show |
| `/admin/invitation/index/{id}/edit` | GET | 编辑邀请码页面 | InvitationController@edit |
| `/admin/invitation/index/{id}` | PUT/PATCH | 更新邀请码 | InvitationController@update |
| `/admin/invitation/index/{id}` | DELETE | 删除邀请码 | InvitationController@destroy |

### 友情链接管理

| 接口路径 | 请求方式 | 功能描述 | 控制器方法 |
| --- | --- | --- | --- |
| `/admin/friendly/index` | GET | 获取友情链接列表 | FriendlyController@index |
| `/admin/friendly/index/create` | GET | 创建友情链接页面 | FriendlyController@create |
| `/admin/friendly/index` | POST | 保存新友情链接 | FriendlyController@store |
| `/admin/friendly/index/{id}` | GET | 查看友情链接详情 | FriendlyController@show |
| `/admin/friendly/index/{id}/edit` | GET | 编辑友情链接页面 | FriendlyController@edit |
| `/admin/friendly/index/{id}` | PUT/PATCH | 更新友情链接 | FriendlyController@update |
| `/admin/friendly/index/{id}` | DELETE | 删除友情链接 | FriendlyController@destroy |

### 网站配置

| 接口路径 | 请求方式 | 功能描述 | 控制器方法 |
| --- | --- | --- | --- |
| `/admin/settings` | GET | 网站配置页面 | SettingsController@index |
