---
description: 项目结构，最佳实践
globs: 
alwaysApply: false
---
# Go Fiber 项目结构规则

本规则描述了基于Go Fiber框架的后端项目结构和最佳实践。

## 目录结构

```
重构项目vue+go/
├── api/                                       # API相关代码
│   ├── dto/                                   # 数据传输对象 (请求/响应结构体)
│   │   ├── admin_user_dto.go                  # 管理员用户DTO
│   │   ├── article_dto.go                     # 文章DTO
│   │   ├── auth_dto.go                        # 认证DTO
│   │   ├── common_dto.go                      # 通用响应DTO
│   │   ├── domain_dto.go                      # 域名DTO
│   │   ├── email.go                           # 邮件DTO
│   │   ├── failed_job_dto.go                  # 失败任务DTO
│   │   ├── friendly_dto.go                    # 友情链接DTO
│   │   ├── invitation_dto.go                  # 邀请码DTO
│   │   ├── job_dto.go                         # 队列任务DTO
│   │   ├── menu_dto.go                        # 菜单DTO
│   │   ├── module_dto.go                      # 模块DTO
│   │   ├── permission_dto.go                  # 权限DTO
│   │   ├── project_dto.go                     # 项目DTO
│   │   ├── role_dto.go                        # 角色DTO
│   │   ├── settings_dto.go                    # 系统设置DTO
│   ├── handlers/                              # HTTP请求处理函数
│   │   ├── admin_user_handler.go              # 管理员用户处理器
│   │   ├── article_handler.go                 # 文章处理器
│   │   ├── auth_handler.go                    # 认证处理器
│   │   ├── domain_handler.go                  # 域名处理器
│   │   ├── email_handler.go                   # 邮件处理器
│   │   ├── failed_job_handler.go              # 失败任务处理器
│   │   ├── friendly_handler.go                # 友情链接处理器
│   │   ├── gateway_handler.go                 # 网关/数据收集处理器
│   │   ├── invitation_handler.go              # 邀请码处理器
│   │   ├── job_handler.go                     # 队列任务处理器
│   │   ├── menu_handler.go                    # 菜单处理器
│   │   ├── module_handler.go                  # 模块处理器
│   │   ├── permission_handler.go              # 权限处理器
│   │   ├── project_content_handler.go         # 项目内容处理器
│   │   ├── project_handler.go                 # 项目处理器
│   │   ├── queue_handler.go                   # 队列管理处理器
│   │   ├── role_handler.go                    # 角色处理器
│   │   ├── settings_handler.go                # 系统设置处理器
│   ├── middleware/                            # 中间件
│   │   ├── auth_middleware.go                 # 认证中间件
│   │   ├── operation_log_middleware.go        # 操作日志中间件
│   └── routes/                                # 路由定义
│       ├── admin_routes.go                    # 管理员相关路由
│       ├── article_routes.go                  # 文章相关路由
│       ├── auth_routes.go                     # 认证相关路由
│       ├── domain_routes.go                   # 域名相关路由
│       ├── email_routes.go                    # 邮件相关路由
│       ├── failed_job_routes.go               # 失败任务相关路由
│       ├── friendly_routes.go                 # 友情链接相关路由
│       ├── gateway_routes.go                  # 网关相关路由
│       ├── invitation_routes.go               # 邀请码相关路由
│       ├── job_routes.go                      # 队列任务相关路由
│       ├── menu_routes.go                     # 菜单相关路由
│       ├── module_routes.go                   # 模块相关路由
│       ├── permission_routes.go               # 权限相关路由
│       ├── project_routes.go                  # 项目相关路由
│       ├── projectscontent_routes.go          # 项目内容相关路由
│       ├── queue_routes.go                    # 队列相关路由
│       ├── role_routes.go                     # 角色相关路由
│       ├── routes.go                          # 路由注册主文件
│       ├── settings_routes.go                 # 系统设置相关路由
├── cmd/                                       # 应用入口
│   ├── server/                                # API服务器入口
│   │   └── main.go                            # API主程序入口，负责启动HTTP服务
│   └── worker/                                # 队列worker入口
│       └── main.go                            # Worker主程序入口，负责启动队列消费进程
├── config/                                    # 应用配置
│   └── config.go                              # 配置结构体和加载逻辑，支持环境变量
├── database/                                  # 数据库连接和迁移
│   ├── database.go                            # 数据库连接与初始化
│   ├── go_fiber.sql                           # MySQL建表脚本
│   ├── 1xs_me.sql                             # 其他SQL脚本
│   └── redis.go                               # Redis连接与初始化
├── docker/                                    # Docker相关配置（可扩展）
├── docs/                                      # API文档与Swagger相关
├── models/                                    # 数据模型定义
│   ├── admin_user.go                          # 管理员用户模型
│   ├── admin_role_users.go                    # 角色-用户关联模型
│   ├── admin_operation_log.go                 # 操作日志模型
│   ├── admin_permission_menu.go               # 权限-菜单关联模型
│   ├── admin_role_menu.go                     # 角色-菜单关联模型
│   ├── admin_role_permission.go               # 角色-权限关联模型
│   ├── article.go                             # 文章模型
│   ├── domain.go                              # 域名模型
│   ├── failed_job.go                          # 失败任务模型
│   ├── friendly.go                            # 友情链接模型
│   ├── invitation.go                          # 邀请码模型
│   ├── job.go                                 # 队列任务模型
│   ├── menu.go                                # 菜单模型
│   ├── migration.go                           # 数据库迁移模型
│   ├── module.go                              # 模块模型
│   ├── permission.go                          # 权限模型
│   ├── project.go                             # 项目模型
│   ├── project_content.go                     # 项目内容模型
│   ├── role.go                                # 角色模型
│   ├── settings.go                            # 系统设置模型
├── test/                                      # 测试代码
│   ├── admin_user_handler_test.go             # 管理员用户接口测试
│   ├── article_handler_test.go                # 文章接口测试
│   ├── getenv_test.go                         # 环境变量测试
│   ├── project_content_handler_test.go        # 项目内容接口测试
│   ├── project_handler_test.go                # 项目接口测试
│   ├── README.md                              # 测试说明
│   ├── test_helpers.go                        # 测试辅助函数
│   └── test_main.go                           # 测试主入口
├── utils/                                     # 通用工具函数
│   ├── captcha.go                             # 验证码生成工具
│   ├── jwt.go                                 # JWT工具
│   ├── password.go                            # 密码加密工具
│   ├── random.go                              # 随机数工具
│   ├── response.go                            # 项目通用响应体
│   ├── template.go                            # 模板渲染工具
│   ├── time.go                                # 时间处理工具
│   ├── validate.go                            # 结构体校验工具
│   ├── email/                                 # 邮件相关工具
│   │   └── email.go                           # 邮件发送与模板渲染
│   ├── export/                                # 数据导出工具
│   │   └── export.go                          # 通用导出逻辑
│   └── queue/                                 # 队列相关工具
│       ├── client.go                          # 队列客户端封装
│       ├── email_util.go                      # 邮件任务入队工具
│       ├── monitor.go                         # 队列监控服务
│       ├── server.go                          # 队列服务端封装与全局失败钩子
│       ├── processors/                        # 队列任务处理器
│       │   ├── data_export_processor.go       # 数据导出任务处理器
│       │   ├── email_processor.go             # 邮件任务处理器
│       │   ├── file_processor.go              # 文件处理任务处理器
│       │   ├── notification_processor.go      # 通知任务处理器
│       │   └── research_processor.go          # 检索任务处理器
│       └── tasks/                             # 队列任务定义
│           └── tasks.go                       # 所有任务类型与payload定义
├── assets/                                    # 静态资源（图片、字体、邮件模板等，省略明细）
├── .env                                       # 环境变量（不纳入版本控制）
├── .env.test                                  # 测试环境变量
├── .gitignore                                 # Git忽略文件
├── go.mod                                     # Go模块定义
├── go.sum                                     # Go模块依赖校验
├── README.md                                  # 项目说明文档
├── run_project_tips.md                        # 项目运行提示
└── 接口文档.md                                 # 接口文档
```

## 核心组件说明

### 主程序入口 (main.go)

主程序入口负责初始化应用程序、连接数据库、设置中间件和路由，并启动HTTP服务器。

### 配置 (config)

配置模块负责从环境变量或配置文件加载和验证应用配置。主要包括数据库、Redis、JWT和应用服务器配置。

### API处理 (api)

- **dto**: 定义请求和响应的数据结构。
  - 请求DTO: 描述客户端发送的数据
  - 响应DTO: 描述服务器返回的数据
  - 通用DTO: 如分页响应、标准响应等
  
  ```go
  // common_dto.go 中定义的标准响应结构
  type StandardResponse struct {
    Code    int         `json:"code"`    // 业务状态码，0表示成功
    Message string      `json:"message"` // 响应消息
    Data    interface{} `json:"data"`    // 响应数据
    Error   string      `json:"error,omitempty"` // 错误信息，仅在开发环境显示
  }
  
  // 分页响应结构
  type PaginatedResponse struct {
    Items      interface{} `json:"items"`       // 当前页数据项
    Total      int64       `json:"total"`       // 总记录数
    Page       int         `json:"page"`        // 当前页码
    PageSize   int         `json:"page_size"`   // 每页大小
    TotalPages int         `json:"total_pages"` // 总页数
  }
  ```

- **handlers**: 实现业务逻辑并处理HTTP请求，负责数据验证、业务处理和响应格式化。

- **middleware**: 定义请求处理前后的中间件，如认证、日志记录、错误处理等。

- **routes**: 定义API路由和对应的处理函数，组织API的整体结构。

### 数据模型 (models)

定义与数据库表对应的Go结构体和相关方法，使用GORM进行对象关系映射。

### 数据库 (database)

管理数据库连接、迁移和种子数据。
- **数据库连接**: 连接MySQL/PostgreSQL等关系型数据库
- **Redis连接**: 用于缓存、会话管理等

### 工具 (utils)

存放可重用的辅助函数：
- **响应工具**: 标准化API响应格式
  
  ```go
  // response.go 中的响应处理函数
  
  // Success 通用成功响应
  func Success(c *fiber.Ctx, message string, data interface{}) error
  
  // Fail 通用失败响应
  func Fail(c *fiber.Ctx, statusCode int, errorCode int, message string, err error) error
  
  // SuccessPaginated 分页成功响应
  func SuccessPaginated(c *fiber.Ctx, message string, items interface{}, total int64, page, pageSize int) error
  
  // 常用错误响应封装
  func BadRequest(c *fiber.Ctx, message string, err error) error
  func Unauthorized(c *fiber.Ctx, message string, err error) error
  func Forbidden(c *fiber.Ctx, message string, err error) error
  func NotFound(c *fiber.Ctx, message string) error
  func ServerError(c *fiber.Ctx, message string, err error) error
  ```

- **验证码工具**: 生成图形验证码
- **JWT工具**: 处理JWT令牌的生成和验证
- **加密工具**: 处理密码加密等

### 静态资源 (assets)

存放项目所需的静态文件，如验证码的字体和背景图。

### 文档 (docs)

包含API文档相关文件，使用Swagger自动生成并维护API文档。

### 测试 (test)

包含API接口测试，使用Go标准测试框架进行集成测试。

## 最佳实践

### 代码组织

1. 使用依赖注入模式管理服务依赖
2. 为每个模块创建接口以便于测试和替换实现
3. 使用清晰的分层架构，保持关注点分离

### 配置管理

1. 使用环境变量配置应用
2. 提供默认配置和配置验证
3. 区分开发、测试和生产环境配置

### 错误处理

1. 对所有可能的错误进行处理
2. 使用统一的错误响应格式
3. 适当记录错误日志，但不泄露敏感信息

### 日志记录

1. 使用结构化日志记录重要事件
2. 包含足够的上下文信息以便于调试
3. 合理设置日志级别

### API设计

1. 遵循RESTful API设计原则
2. 使用中间件实现横切关注点，如认证、日志、错误处理等
3. 使用DTO来明确API的输入和输出结构
4. 为所有API端点编写Swagger文档注释
5. 使用标准化的响应格式，包括成功响应和错误响应
6. 对列表数据实现分页功能，使用统一的分页响应结构

### 测试实践

1. 编写单元测试验证业务逻辑
2. 编写集成测试验证API行为
3. 使用测试环境数据库，避免影响生产数据
4. 创建测试辅助函数简化测试代码

### 文档生成

1. 为API处理函数添加Swagger注释
2. 使用swag工具自动生成API文档
3. 保持文档与代码同步更新
4. 提供文档查看界面方便开发和调试

### 安全实践

1. 验证所有用户输入
2. 使用HTTPS保护数据传输
3. 正确存储和验证用户密码
4. 实施适当的认证和授权机制
5. 防止常见安全漏洞，如SQL注入、XSS等