package handlers

import (
	"math"
	"strconv"
	"log"

	"go-fiber-api/api/dto"
	"go-fiber-api/database"
	"go-fiber-api/models"
	"go-fiber-api/utils"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

// @Summary 获取管理员用户列表
// @Description 获取管理员用户列表，支持分页和按用户名、姓名、邮箱、状态过滤
// @Tags AdminUsers
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param username query string false "用户名 (模糊查询)"
// @Param name query string false "姓名 (模糊查询)"
// @Param email query string false "邮箱 (模糊查询)"
// @Param state query int false "状态 (1:启用, 其他或不传:所有)"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10) maximum(100)
// @Success 200 {object} dto.StandardResponse{data=dto.PaginatedResponse{items=[]dto.AdminUserResponse}} "成功响应"
// @Failure 400 {object} dto.StandardResponse "无效的查询参数"
// @Failure 500 {object} dto.StandardResponse "服务器内部错误"
// @Router /admin/users [get]
func GetAdminUsers(c *fiber.Ctx) error {
	// 获取查询参数
	query := new(dto.AdminUserQueryDTO)
	if err := c.QueryParser(query); err != nil {
		return utils.BadRequest(c, "无效的查询参数", err)
	}

	// 设置默认分页
	page := query.Page
	if page <= 0 {
		page = 1
	}
	pageSize := query.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	var users []models.AdminUser
	var total int64

	// 构建查询
	db := database.DB.Model(&models.AdminUser{})

	// 应用过滤条件
	if query.Username != "" {
		db = db.Where("username LIKE ?", "%"+query.Username+"%")
	}
	if query.Name != "" {
		db = db.Where("name LIKE ?", "%"+query.Name+"%")
	}
	if query.Email != "" {
		db = db.Where("email LIKE ?", "%"+query.Email+"%")
	}
	if query.State != 0 {
		db = db.Where("state = ?", query.State)
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return utils.ServerError(c, "获取用户总数失败", err)
	}

	// 获取分页数据
	result := db.Limit(pageSize).Offset(offset).Find(&users)
	if result.Error != nil {
		return utils.ServerError(c, "获取用户列表失败", result.Error)
	}

	// 构建响应
	var responseItems []dto.AdminUserResponse
	for _, user := range users {
		var lastedAt string
		if user.LastedAt != nil {
			lastedAt = user.LastedAt.Format("2006-01-02 15:04:05")
		}

		responseItems = append(responseItems, dto.AdminUserResponse{
			ID:              user.ID,
			Username:        user.Username,
			Name:            user.Name,
			Email:           user.Email,
			Avatar:          user.Avatar,
			Referee:         user.Referee,
			Notify:          user.Notify,
			LastedIPAddress: user.LastedIPAddress,
			LastedAt:        lastedAt,
			State:           user.State,
			CreatedAt:       user.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:       user.UpdatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	// 计算总页数
	totalPages := int(math.Ceil(float64(total) / float64(pageSize)))

	// 使用新的成功响应函数
	return utils.Success(c, "获取用户列表成功", dto.PaginatedResponse{
		Items:      responseItems,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	})
}

// @Summary 获取单个管理员用户
// @Description 根据ID获取管理员用户的详细信息
// @Tags AdminUsers
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "用户ID"
// @Success 200 {object} dto.StandardResponse{data=dto.AdminUserResponse} "成功响应"
// @Failure 404 {object} dto.StandardResponse "用户未找到"
// @Router /admin/users/{id} [get]
func GetAdminUser(c *fiber.Ctx) error {
	id := c.Params("id")

	var user models.AdminUser
	result := database.DB.Model(&models.AdminUser{}).Preload("Roles").First(&user, id)
	if result.Error != nil {
		return utils.NotFound(c, "用户不存在")
	}

	var lastedAt string
	if user.LastedAt != nil {
		lastedAt = user.LastedAt.Format("2006-01-02 15:04:05")
	}

	// 获取用户角色
	var roles []models.Role
	if err := database.DB.Model(&user).Association("Roles").Find(&roles); err != nil {
		log.Printf("获取用户角色失败: %v", err)
	}

	// 获取用户通过角色获得的权限
	var permissions []models.Permission
	if len(roles) > 0 {
		var roleIDs []uint64
		for _, role := range roles {
			roleIDs = append(roleIDs, role.ID)
		}
		
		if err := database.DB.Raw(`
			SELECT DISTINCT p.* FROM admin_permissions p
			JOIN admin_role_permissions rp ON p.id = rp.permission_id
			WHERE rp.role_id IN ?
		`, roleIDs).Find(&permissions).Error; err != nil {
			log.Printf("获取用户权限失败: %v", err)
		}
	}

	// 构建响应
	response := dto.AdminUserResponse{
		ID:              user.ID,
		Username:        user.Username,
		Name:            user.Name,
		Email:           user.Email,
		Avatar:          user.Avatar,
		Referee:         user.Referee,
		Notify:          user.Notify,
		LastedIPAddress: user.LastedIPAddress,
		LastedAt:        lastedAt,
		State:           user.State,
		CreatedAt:       user.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:       user.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	// 附加角色和权限信息
	responseData := fiber.Map{
		"user": response,
		"roles": roles,
		"permissions": permissions,
	}

	// 使用新的成功响应函数
	return utils.Success(c, "获取用户成功", responseData)
}

// @Summary 创建管理员用户
// @Description 创建一个新的管理员用户
// @Tags AdminUsers
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param user body dto.AdminUserCreateDTO true "创建用户请求体"
// @Success 200 {object} dto.StandardResponse{data=object{id=int}} "创建成功，返回用户ID"
// @Failure 400 {object} dto.StandardResponse "无效的请求数据或用户名已存在"
// @Failure 500 {object} dto.StandardResponse "服务器内部错误"
// @Router /admin/users [post]
func CreateAdminUser(c *fiber.Ctx) error {
	// 解析请求体
	createDTO := new(dto.AdminUserCreateDTO)
	if err := c.BodyParser(createDTO); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}

	// 验证请求数据
	// TODO: 使用验证器验证数据

	// 检查用户名是否存在
	var existingUser models.AdminUser
	if err := database.DB.Where("username = ?", createDTO.Username).First(&existingUser).Error; err == nil {
		return utils.BadRequest(c, "用户名已存在", nil)
	}

	// 创建用户
	user := models.AdminUser{
		Username:        createDTO.Username,
		Password:        createDTO.Password, // 注意：实际应用中应该加密密码
		Name:            createDTO.Name,
		Email:           createDTO.Email,
		Avatar:          createDTO.Avatar,
		Referee:         createDTO.Referee,
		Notify:          createDTO.Notify,
		State:           createDTO.State,
		LastedIPAddress: c.IP(),
	}

	result := database.DB.Create(&user)
	if result.Error != nil {
		return utils.ServerError(c, "创建用户失败", result.Error)
	}

	// 创建用户-角色关联
	if len(createDTO.RoleIDs) > 0 {
		// 使用事务处理关联操作
		err := database.DB.Transaction(func(tx *gorm.DB) error {
			// 为用户添加角色
			for _, roleID := range createDTO.RoleIDs {
				roleUser := models.AdminRoleUser{
					RoleID: roleID,
					UserID: user.ID,
				}
				if err := tx.Create(&roleUser).Error; err != nil {
					return err
				}
			}
			return nil
		})
		
		if err != nil {
			// 记录错误但不中断响应
			log.Printf("为用户 %d 分配角色失败: %v", user.ID, err)
		}
	} else {
		// 如果没有指定角色，分配默认角色
		roleUser := models.AdminRoleUser{
			RoleID: 2, // 默认角色ID，通常是普通用户角色
			UserID: user.ID,
		}
		
		if err := database.DB.Create(&roleUser).Error; err != nil {
			log.Printf("为用户 %d 分配默认角色失败: %v", user.ID, err)
		}
	}

	// 处理用户权限关联
	if len(createDTO.PermissionIDs) > 0 {
		log.Printf("为用户 %d 添加 %d 个直接权限", user.ID, len(createDTO.PermissionIDs))
		
		// 获取用户的角色ID
		var roleID uint64
		
		// 查询是否已经分配了角色
		var roleUser models.AdminRoleUser
		if err := database.DB.Where("user_id = ?", user.ID).First(&roleUser).Error; err != nil {
			// 如果没有角色，创建一个默认角色关联
			log.Printf("用户 %d 没有关联角色，使用默认角色", user.ID)
			roleID = 2 // 默认角色ID
		} else {
			roleID = roleUser.RoleID
		}
		
		// 使用事务处理权限关联
		err := database.DB.Transaction(func(tx *gorm.DB) error {
			// 为用户角色添加权限
			for _, permID := range createDTO.PermissionIDs {
				rolePerm := models.AdminUserPermission{
					RoleID:       roleID,
					PermissionID: permID,
				}
				if err := tx.Create(&rolePerm).Error; err != nil {
					return err
				}
			}
			return nil
		})
		
		if err != nil {
			log.Printf("为用户 %d 的角色(ID=%d)分配权限失败: %v", user.ID, roleID, err)
		} else {
			log.Printf("为用户 %d 的角色(ID=%d)分配了 %d 个权限", user.ID, roleID, len(createDTO.PermissionIDs))
		}
	}

	// 使用新的成功响应函数
	return utils.Success(c, "用户创建成功", fiber.Map{"id": user.ID})
}

// @Summary 更新管理员用户
// @Description 根据ID更新管理员用户的信息
// @Tags AdminUsers
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "用户ID"
// @Param user body dto.AdminUserUpdateDTO true "更新用户请求体 (只需提供要更新的字段)"
// @Success 200 {object} dto.StandardResponse "更新成功"
// @Failure 400 {object} dto.StandardResponse "无效的用户ID、请求数据或用户名已被使用"
// @Failure 404 {object} dto.StandardResponse "用户未找到"
// @Failure 500 {object} dto.StandardResponse "服务器内部错误"
// @Router /admin/users/{id} [put]
func UpdateAdminUser(c *fiber.Ctx) error {
	id, err := strconv.Atoi(c.Params("id"))
	if err != nil {
		return utils.BadRequest(c, "无效的用户ID", err)
	}

	// 解析请求体
	updateDTO := new(dto.AdminUserUpdateDTO)
	if err := c.BodyParser(updateDTO); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}

	// 检查用户是否存在
	var user models.AdminUser
	if err := database.DB.First(&user, id).Error; err != nil {
		return utils.NotFound(c, "用户不存在")
	}

	// 更新用户信息
	if updateDTO.Username != "" {
		// 检查用户名是否被其他用户使用
		var existingUser models.AdminUser
		if err := database.DB.Where("username = ? AND id != ?", updateDTO.Username, id).First(&existingUser).Error; err == nil {
			return utils.BadRequest(c, "用户名已被使用", nil)
		}
		user.Username = updateDTO.Username
	}

	if updateDTO.Password != "" {
		user.Password = updateDTO.Password // 注意：实际应用中应该加密密码
	}

	if updateDTO.Name != "" {
		user.Name = updateDTO.Name
	}

	if updateDTO.Email != "" {
		user.Email = updateDTO.Email
	}

	if updateDTO.Avatar != "" {
		user.Avatar = updateDTO.Avatar
	}

	if updateDTO.Referee != "" {
		user.Referee = updateDTO.Referee
	}

	user.Notify = updateDTO.Notify

	if updateDTO.State != 0 {
		user.State = updateDTO.State
	}

	// 使用事务处理用户更新和角色分配
	err = database.DB.Transaction(func(tx *gorm.DB) error {
		// 保存用户基本信息更新
		if err := tx.Save(&user).Error; err != nil {
			return err
		}
		
		// 如果请求中包含角色ID数组，则更新用户角色
		if updateDTO.RoleIDs != nil {
			// 先删除现有的角色关联
			if err := tx.Where("user_id = ?", user.ID).Delete(&models.AdminRoleUser{}).Error; err != nil {
				return err
			}
			
			// 添加新的角色关联
			for _, roleID := range updateDTO.RoleIDs {
				roleUser := models.AdminRoleUser{
					RoleID: roleID,
					UserID: user.ID,
				}
				if err := tx.Create(&roleUser).Error; err != nil {
					return err
				}
			}
			
			log.Printf("已更新用户 %d 的角色关联, 角色IDs: %v", user.ID, updateDTO.RoleIDs)
		}
		
		// 如果请求中包含权限ID数组，则更新用户直接权限
		if updateDTO.PermissionIDs != nil {
			// 先删除现有的权限关联
			if err := tx.Where("role_id IN (SELECT role_id FROM admin_role_users WHERE user_id = ?)", user.ID).Delete(&models.AdminUserPermission{}).Error; err != nil {
				log.Printf("删除用户 %d 的角色权限关联失败: %v", user.ID, err)
				return err
			}
			
			// 获取用户的所有角色ID
			var roleIDs []uint64
			if err := tx.Model(&models.AdminRoleUser{}).Where("user_id = ?", user.ID).Pluck("role_id", &roleIDs).Error; err != nil {
				log.Printf("获取用户 %d 的角色失败: %v", user.ID, err)
				return err
			}
			
			// 如果用户有角色，则为每个角色添加权限
			if len(roleIDs) > 0 {
				// 使用第一个角色ID作为主要角色
				primaryRoleID := roleIDs[0]
				
				// 添加新的权限关联
				for _, permID := range updateDTO.PermissionIDs {
					rolePerm := models.AdminUserPermission{
						RoleID:       primaryRoleID,
						PermissionID: permID,
					}
					if err := tx.Create(&rolePerm).Error; err != nil {
						return err
					}
				}
				
				log.Printf("已更新用户 %d 的角色(ID=%d)权限关联, 权限IDs: %v", user.ID, primaryRoleID, updateDTO.PermissionIDs)
			} else {
				log.Printf("用户 %d 没有关联角色，无法设置权限", user.ID)
			}
		}
		
		return nil
	})
	
	if err != nil {
		return utils.ServerError(c, "更新用户失败", err)
	}

	// 使用新的成功响应函数
	return utils.Success(c, "用户更新成功", nil)
}

// @Summary 删除管理员用户
// @Description 根据ID删除管理员用户 (软删除)
// @Tags AdminUsers
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "用户ID"
// @Success 200 {object} dto.StandardResponse "删除成功"
// @Failure 404 {object} dto.StandardResponse "用户未找到"
// @Failure 500 {object} dto.StandardResponse "服务器内部错误"
// @Router /admin/users/{id} [delete]
func DeleteAdminUser(c *fiber.Ctx) error {
	id := c.Params("id")

	// 检查用户是否存在
	var user models.AdminUser
	if err := database.DB.First(&user, id).Error; err != nil {
		return utils.NotFound(c, "用户不存在")
	}

	// 软删除用户
	if err := database.DB.Delete(&user).Error; err != nil {
		return utils.ServerError(c, "删除用户失败", err)
	}

	// 使用新的成功响应函数
	return utils.Success(c, "用户删除成功", nil)
}
