package routes

import (
	"sync"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/websocket/v2"
)

// 存储所有WebSocket连接的客户端
var (
	wsClients = make(map[string]*websocket.Conn)
	wsMutex   = sync.RWMutex{}
)

// GetWebSocketClients 获取所有WebSocket客户端
func GetWebSocketClients() map[string]*websocket.Conn {
	wsMutex.RLock()
	defer wsMutex.RUnlock()
	return wsClients
}

// BroadcastToWebSocketClients 广播消息到所有WebSocket客户端
func BroadcastToWebSocketClients(message []byte) {
	wsMutex.RLock()
	defer wsMutex.RUnlock()

	for _, client := range wsClients {
		if err := client.WriteMessage(websocket.TextMessage, message); err != nil {
			// 如果发送失败，可能客户端已断开，但我们不在这里移除它
			// 移除操作应该在各自的处理器中进行
			continue
		}
	}
}

// SetupWebSocketRoutes 设置WebSocket相关路由
func SetupWebSocketRoutes(app *fiber.App) {
	// WebSocket路由
	app.Get("/ws", func(c *fiber.Ctx) error {
		// 返回WebSocket客户端页面
		return c.SendString(`
<!DOCTYPE html>
<html>
<head>
    <title>实时通知客户端</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        #notifications { border: 1px solid #ccc; padding: 10px; height: 300px; overflow-y: auto; margin-bottom: 10px; }
        .notification { padding: 8px; margin-bottom: 5px; background-color: #f0f0f0; border-radius: 4px; }
        .notification.article { background-color: #e0f7fa; }
        .time { font-size: 0.8em; color: #777; }
        .title { font-weight: bold; }
        .content { margin-top: 5px; }
    </style>
</head>
<body>
    <h1>实时通知</h1>
    <div id="notifications"></div>
    <div id="status">未连接</div>

    <script>
        const notificationsDiv = document.getElementById('notifications');
        const statusDiv = document.getElementById('status');
        let socket;

        function connectWebSocket() {
            // 根据当前页面的URL创建WebSocket连接
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = protocol + '//' + window.location.host + '/ws/connect';
            
            statusDiv.textContent = '正在连接...';
            socket = new WebSocket(wsUrl);
            
            socket.onopen = function() {
                statusDiv.textContent = '已连接';
                statusDiv.style.color = 'green';
            };
            
            socket.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    addNotification(data);
                } catch (e) {
                    console.error('解析消息失败:', e);
                    addNotification({
                        type: 'error',
                        title: '无法解析的消息',
                        content: event.data
                    });
                }
            };
            
            socket.onclose = function() {
                statusDiv.textContent = '连接已关闭，5秒后重连...';
                statusDiv.style.color = 'red';
                setTimeout(connectWebSocket, 5000);
            };
            
            socket.onerror = function(error) {
                console.error('WebSocket错误:', error);
                statusDiv.textContent = '连接错误';
                statusDiv.style.color = 'red';
            };
        }
        
        function addNotification(data) {
            const notificationDiv = document.createElement('div');
            notificationDiv.className = 'notification ' + (data.type || 'default');
            
            const timeDiv = document.createElement('div');
            timeDiv.className = 'time';
            timeDiv.textContent = data.time || new Date().toLocaleString();
            
            const titleDiv = document.createElement('div');
            titleDiv.className = 'title';
            titleDiv.textContent = data.title || '无标题通知';
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'content';
            contentDiv.textContent = data.content || '';
            
            notificationDiv.appendChild(timeDiv);
            notificationDiv.appendChild(titleDiv);
            notificationDiv.appendChild(contentDiv);
            
            notificationsDiv.appendChild(notificationDiv);
            notificationsDiv.scrollTop = notificationsDiv.scrollHeight;
        }
        
        // 页面加载时连接WebSocket
        window.onload = connectWebSocket;
    </script>
</body>
</html>
		`)
	})

	// 配置WebSocket路由，确保CORS设置正确
	app.Use("/ws/connect", func(c *fiber.Ctx) error {
		// 设置CORS头，允许所有来源
		c.Set("Access-Control-Allow-Origin", "*")
		c.Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
		c.Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
		
		// 处理预检请求
		if c.Method() == "OPTIONS" {
			return c.SendStatus(fiber.StatusOK)
		}
		
		// IsWebSocketUpgrade检查请求是否是WebSocket升级请求
		if websocket.IsWebSocketUpgrade(c) {
			// 在升级之前保存客户端IP
			c.Locals("client_ip", c.IP())
			return c.Next()
		}
		return fiber.ErrUpgradeRequired
	})

	// WebSocket连接端点
	app.Get("/ws/connect", websocket.New(func(c *websocket.Conn) {
		// 生成一个唯一的客户端ID
		clientID := time.Now().Format("20060102150405") + "-" + time.Now().String()

		// 记录连接信息
		wsMutex.Lock()
		wsClients[clientID] = c
		clientCount := len(wsClients)
		wsMutex.Unlock()

		// 打印连接信息到服务器日志
		println("WebSocket客户端已连接: ", clientID, "，当前连接数: ", clientCount)

		// 发送欢迎消息
		c.WriteJSON(map[string]interface{}{
			"type":    "system",
			"title":   "连接成功",
			"content": "您已成功连接到WebSocket服务器",
			"time":    time.Now().Format("2006-01-02 15:04:05"),
		})

		// 处理消息
		for {
			_, msg, err := c.ReadMessage()
			if err != nil {
				wsMutex.Lock()
				delete(wsClients, clientID)
				wsMutex.Unlock()
				println("WebSocket客户端已断开: ", clientID)
				break
			}

			// 简单地回显消息
			c.WriteMessage(websocket.TextMessage, msg)
		}
	}, websocket.Config{
		ReadBufferSize:  1024,
		WriteBufferSize: 1024,
	}))
}
