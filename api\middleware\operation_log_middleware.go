package middleware

import (
	"encoding/json"
	"fmt"
	"go-fiber-api/database"
	"go-fiber-api/models"
	"go-fiber-api/utils"
	"log"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// OperationLogMiddleware 记录管理员操作日志的中间件
func OperationLogMiddleware() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// 记录请求开始时间
		startTime := time.Now()
		
		// 先保存一些请求信息，因为执行完c.Next()后可能会被修改
		method := c.Method()
		path := c.Path()
		ip := c.IP()
		body := c.Body()
		
		// 执行下一个处理器
		err := c.Next()
		
		// 只记录特定HTTP方法的请求
		if method != "GET" && method != "HEAD" && method != "OPTIONS" {
			// 获取数据库连接
			db := database.DB
			if db == nil {
				logrus.Error("数据库连接失败，无法记录操作日志")
				return err
			}
			
			// 获取当前用户ID
			var userID uint64 = 0
			
			// 尝试从token获取用户ID，忽略错误（未登录用户ID为0）
			userID = utils.GetUserIDFromContext(c)
			log.Printf("操作日志中间件: 获取到用户ID: %d", userID)
			
			// 处理请求体
			var input string
			if len(body) > 0 {
				var bodyMap map[string]interface{}
				if err := json.Unmarshal(body, &bodyMap); err != nil {
					// 如果无法解析为JSON，则使用原始字符串
					input = fmt.Sprintf("{\"raw\": %q}", string(body))
					log.Printf("操作日志中间件: 请求体解析失败: %v", err)
				} else {
					// 移除敏感信息，如密码
					removePasswordFields(bodyMap)
					// 将处理后的请求体转换为JSON字符串
					if inputJSON, jsonErr := json.Marshal(bodyMap); jsonErr == nil {
						input = string(inputJSON)
					} else {
						input = "{\"error\": \"无法序列化请求体\"}"
						log.Printf("操作日志中间件: 请求体序列化失败: %v", jsonErr)
					}
				}
				
				// 限制日志大小，防止过大的请求体
				if len(input) > 10000 {
					input = input[:10000] + "...(truncated)"
				}
			} else {
				input = "{}"
			}
			
			// 创建操作日志记录
			operationLog := models.AdminOperationLog{
				UserID:    userID,
				Path:      path,
				Method:    method,
				IP:        ip,
				Input:     input,
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
				Duration:  time.Since(startTime).Milliseconds(), // 记录请求处理时间
			}
			
			log.Printf("操作日志中间件: 准备保存日志记录: %+v", operationLog)
			
			// 使用独立的事务保存操作日志
			tx := db.Session(&gorm.Session{})
			if dbErr := tx.Create(&operationLog).Error; dbErr != nil {
				logrus.WithError(dbErr).Error("保存操作日志失败")
				log.Printf("操作日志中间件: 保存日志失败: %v", dbErr)
			} else {
				log.Printf("操作日志中间件: 成功保存日志记录，ID: %d", operationLog.ID)
			}
		}
		
		return err
	}
}

// removePasswordFields 从请求体中移除密码字段
func removePasswordFields(data map[string]interface{}) {
	sensitiveFields := []string{"password", "pwd", "passwd", "secret", "token"}
	
	for key, val := range data {
		// 检查是否为敏感字段
		for _, field := range sensitiveFields {
			if key == field {
				data[key] = "******" // 替换为星号
				break
			}
		}
		
		// 如果是嵌套对象，递归处理
		if nestedMap, ok := val.(map[string]interface{}); ok {
			removePasswordFields(nestedMap)
		} else if nestedSlice, ok := val.([]interface{}); ok {
			// 处理数组中的嵌套对象
			for _, item := range nestedSlice {
				if nestedMap, ok := item.(map[string]interface{}); ok {
					removePasswordFields(nestedMap)
				}
			}
		}
	}
} 