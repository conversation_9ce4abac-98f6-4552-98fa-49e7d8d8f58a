package tasks

import (
	"encoding/json"
	"go-fiber-api/utils/email"

	"github.com/hibiken/asynq"
)

// 任务类型常量
const (
	// 邮件相关任务
	TypeEmailSend = "email:send"

	// 通知相关任务
	TypeNotificationSend = "notification:send"

	// 文件处理相关任务
	TypeFileProcess = "file:process"

	// 数据导出相关任务
	TypeDataExport = "data:export"

	// 公告发布任务
	TypeArticlePublish = "article:publish"
)

// 邮件任务负载
type EmailPayload = email.EmailData

// 通知任务负载
type NotificationPayload struct {
	UserID  uint64 `json:"user_id"`
	Title   string `json:"title"`
	Content string `json:"content"`
	Type    string `json:"type"`
}

// 文件处理任务负载
type FileProcessPayload struct {
	FilePath    string `json:"file_path"`
	ProcessType string `json:"process_type"`
}

// 数据导出任务负载
type DataExportPayload struct {
	UserID     uint64 `json:"user_id"`
	ExportType string `json:"export_type"`
	Filters    string `json:"filters"`
}

// ArticlePublishPayload 公告发布任务负载
type ArticlePublishPayload struct {
	ArticleID uint64 `json:"article_id"`
	Title     string `json:"title"`
	Content   string `json:"content"`
	Author    string `json:"author"`
}

// NewEmailTask 创建邮件任务
func NewEmailTask(to, subject, body string, isHTML bool) (*asynq.Task, error) {
	payload, err := json.Marshal(EmailPayload{
		To:      []string{to},
		Subject: subject,
		Body:    body,
		IsHTML:  isHTML,
	})
	if err != nil {
		return nil, err
	}
	return asynq.NewTask(TypeEmailSend, payload), nil
}

// NewNotificationTask 创建通知任务
func NewNotificationTask(userID uint64, title, content, notificationType string) (*asynq.Task, error) {
	payload, err := json.Marshal(NotificationPayload{
		UserID:  userID,
		Title:   title,
		Content: content,
		Type:    notificationType,
	})
	if err != nil {
		return nil, err
	}
	return asynq.NewTask(TypeNotificationSend, payload), nil
}

// NewArticlePublishTask 创建公告发布任务
func NewArticlePublishTask(articleID uint64, title, content, author string) (*asynq.Task, error) {
	payload, err := json.Marshal(ArticlePublishPayload{
		ArticleID: articleID,
		Title:     title,
		Content:   content,
		Author:    author,
	})
	if err != nil {
		return nil, err
	}
	return asynq.NewTask(TypeArticlePublish, payload), nil
}

// NewFileProcessTask 创建文件处理任务
func NewFileProcessTask(filePath, processType string) (*asynq.Task, error) {
	payload, err := json.Marshal(FileProcessPayload{
		FilePath:    filePath,
		ProcessType: processType,
	})
	if err != nil {
		return nil, err
	}
	return asynq.NewTask(TypeFileProcess, payload), nil
}

// NewDataExportTask 创建数据导出任务
func NewDataExportTask(userID uint64, exportType, filters string) (*asynq.Task, error) {
	payload, err := json.Marshal(DataExportPayload{
		UserID:     userID,
		ExportType: exportType,
		Filters:    filters,
	})
	if err != nil {
		return nil, err
	}
	return asynq.NewTask(TypeDataExport, payload), nil
}