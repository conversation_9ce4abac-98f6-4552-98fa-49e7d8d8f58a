package models

import (
	"time"

	"gorm.io/gorm"
)

// ProjectContent 项目内容模型
type ProjectContent struct {
	CID        uint64         `json:"cid" gorm:"primaryKey;column:cid;not null;comment:内容ID"`
	ProjectID  uint64         `json:"project_id" gorm:"not null;default:0;comment:项目ID"`
	UserID     uint64         `json:"user_id" gorm:"not null;comment:用户ID"`
	Content    string         `json:"content" gorm:"type:mediumtext;not null;comment:接收的内容"`
	Server     string         `json:"server" gorm:"type:mediumtext;not null;comment:服务器内容"`
	Domain     string         `json:"domain" gorm:"not null;size:255;comment:域名"`
	Hash       string         `json:"hash" gorm:"not null;size:32;comment:HASH"`
	State      int8           `json:"state" gorm:"type:tinyint(1);not null;default:1;comment:状态:0 无效;1 启用"`
	CreatedAt  time.Time      `json:"created_at" gorm:"comment:创建时间"`
	UpdatedAt  time.Time      `json:"updated_at" gorm:"comment:更新时间"`
	DeletedAt  gorm.DeletedAt `json:"-" gorm:"index;comment:删除时间"`
}

// TableName 指定表名
func (ProjectContent) TableName() string {
	return "projects_content"
} 