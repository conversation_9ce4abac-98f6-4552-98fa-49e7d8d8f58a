<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>欢迎加入</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            border-radius: 8px;
            padding: 30px;
            background-color: #ffffff;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            padding-bottom: 15px;
            border-bottom: 1px solid #eaeaea;
            margin-bottom: 25px;
        }

        .header h2 {
            color: #3a3a3a;
            margin: 0;
            font-weight: 600;
        }

        .logo {
            text-align: center;
            margin-bottom: 20px;
        }

        .content {
            color: #555;
            font-size: 15px;
        }

        .login-button {
            display: inline-block;
            background-color: #4285f4;
            color: white !important;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 4px;
            margin: 25px 0;
            font-weight: 500;
            transition: background-color 0.3s ease;
            text-align: center;
        }

        .login-button:hover {
            background-color: #3367d6;
        }

        .feature-list {
            margin: 20px 0;
            padding-left: 20px;
        }

        .feature-list li {
            margin-bottom: 10px;
        }

        .footer {
            margin-top: 30px;
            font-size: 13px;
            text-align: center;
            color: #888;
            padding-top: 15px;
            border-top: 1px solid #eaeaea;
        }

        p {
            margin: 16px 0;
        }

        @media only screen and (max-width: 600px) {
            body {
                padding: 10px;
            }
            
            .container {
                padding: 20px;
            }

            .login-button {
                display: block;
                text-align: center;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h2>欢迎加入</h2>
        </div>
        <div class="content">
            <p>尊敬的 <strong>{{.Username}}</strong>：</p>
            <p>感谢您注册我们的服务！您的账户已经成功创建，现在您可以开始使用我们的平台了。</p>
            
            <p>我们的平台提供以下功能：</p>
            <ul class="feature-list">
                <li>XSS漏洞测试与检测</li>
                <li>安全报告生成与分析</li>
                <li>多种测试模块与场景</li>
                <li>实时监控与预警</li>
            </ul>
            
            <p style="text-align: center;">
                <a href="{{.LoginLink}}" class="login-button">立即登录</a>
            </p>
            
            <p>如果您有任何问题或需要帮助，请随时联系我们的支持团队。</p>
        </div>
        <div class="footer">
            <p>此邮件由系统自动发送，请勿回复。</p>
            <p>© 2025 XSS测试平台 | 安全中心</p>
        </div>
    </div>
</body>

</html> 