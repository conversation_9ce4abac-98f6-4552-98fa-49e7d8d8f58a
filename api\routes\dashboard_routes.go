package routes

import (
	"go-fiber-api/api/handlers"
	"go-fiber-api/api/middleware"
	"go-fiber-api/utils"

	"github.com/gofiber/fiber/v2"
)

// SetupDashboardRoutes 设置控制面板相关路由
func SetupDashboardRoutes(router fiber.Router) {
	// 控制面板路由组
	dashboard := router.Group("/dashboard")

	// 添加认证中间件
	dashboard.Use(utils.RequireAuthentication)

	// 系统概览数据
	dashboard.Get("/", middleware.RequirePermission("dashboard.view"), handlers.GetDashboardData)

	// 系统信息
	dashboard.Get("/system", middleware.RequirePermission("dashboard.view"), handlers.GetSystemInfo)

	// 操作日志
	dashboard.Get("/logs", middleware.RequirePermission("dashboard.logs"), handlers.GetRecentOperationLogs)

} 