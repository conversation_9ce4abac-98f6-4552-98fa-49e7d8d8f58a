package handlers

import (
	"go-fiber-api/utils/email"
	"go-fiber-api/utils/queue"
	"go-fiber-api/api/dto"

	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
)

// 创建验证器实例
var validate = validator.New()


// SendEmail 发送邮件
// @Summary      发送邮件
// @Description  立即将邮件请求加入发送队列
// @Tags         Email
// @Accept       json
// @Produce      json
// @Param        email body dto.EmailRequest true "邮件请求体"
// @Success      200 {object} dto.StandardResponse "邮件已加入发送队列"
// @Failure      400 {object} dto.StandardResponse "请求参数错误"
// @Failure      500 {object} dto.StandardResponse "服务器内部错误"
// @Router       /emails/send [post]
func SendEmail(c *fiber.Ctx) error {
	// 解析请求
	var req dto.EmailRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "无法解析请求: " + err.Error(),
		})
	}

	// 验证请求
	if err := validate.Struct(req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "验证失败: " + err.Error(),
		})
	}
	// 构造EmailData并入队
	emailData := email.EmailData{
		To:      []string{req.To},
		Subject: req.Subject,
		Body:    req.Body,
	}
	if err := queue.EnqueueEmailTask(emailData); err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "邮件入队失败: " + err.Error(),
		})
	}
	return c.JSON(fiber.Map{
		"message": "邮件已加入发送队列",
	})
}

// SendScheduledEmail 发送定时邮件
// @Summary      发送定时邮件
// @Description  将邮件请求加入队列，支持延迟发送
// @Tags         Email
// @Accept       json
// @Produce      json
// @Param        email body dto.ScheduledEmailRequest true "定时邮件请求体"
// @Success      200 {object} dto.StandardResponse "邮件已加入发送队列"
// @Failure      400 {object} dto.StandardResponse "请求参数错误"
// @Failure      500 {object} dto.StandardResponse "服务器内部错误"
// @Router       /emails/scheduled [post]
func SendScheduledEmail(c *fiber.Ctx) error {
	// 解析请求
	var req dto.ScheduledEmailRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "无法解析请求: " + err.Error(),
		})
	}

	// 验证请求
	if err := validate.Struct(req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "验证失败: " + err.Error(),
		})
	}
	// 目前EnqueueEmailTask不支持延迟，可扩展支持；此处直接入队
	emailData := email.EmailData{
		To:      []string{req.To},
		Subject: req.Subject,
		Body:    req.Body,
	}
	if err := queue.EnqueueEmailTask(emailData); err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "邮件入队失败: " + err.Error(),
		})
	}
	return c.JSON(fiber.Map{
		"message": "邮件已加入发送队列",
	})
} 