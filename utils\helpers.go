package utils

import (
	"fmt"
	"strings"
)

// ContainsUint64 检查slice中是否包含指定的uint64值
// 返回true表示包含，false表示不包含
func ContainsUint64(slice []uint64, val uint64) bool {
	for _, item := range slice {
		if item == val {
			return true
		}
	}
	return false
}

// StringToSlice 将字符串按指定分隔符分割为字符串切片
// 例如："1,2,3" => ["1", "2", "3"]
func StringToSlice(s string, sep string) []string {
	if s == "" {
		return []string{}
	}
	return strings.Split(s, sep)
}

// TrimSpace 移除字符串两端的空白字符
func TrimSpace(s string) string {
	return strings.TrimSpace(s)
}

// IsEmpty 检查字符串是否为空(空字符串或只包含空白字符)
func IsEmpty(s string) bool {
	return len(strings.TrimSpace(s)) == 0
}

// GetPageAndSize 从查询参数中获取分页参数
// 返回页码和每页大小，如果参数无效则使用默认值
func GetPageAndSize(pageStr, sizeStr string, defaultPage, defaultSize int) (int, int) {
	page := defaultPage
	size := defaultSize

	if pageStr != "" {
		if p, err := ParseInt(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if sizeStr != "" {
		if s, err := ParseInt(sizeStr); err == nil && s > 0 {
			size = s
		}
	}

	return page, size
}

// ParseInt 将字符串转换为整数
func ParseInt(s string) (int, error) {
	var result int
	_, err := fmt.Sscanf(s, "%d", &result)
	return result, err
} 