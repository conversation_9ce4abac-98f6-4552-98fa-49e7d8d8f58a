package main

import (
	"fmt"
	"log"
	"time"

	"github.com/joho/godotenv"

	"go-fiber-api/database"
	"go-fiber-api/models"
	"go-fiber-api/utils"
)

func main() {
	// 加载环境变量
	if err := godotenv.Load(); err != nil {
		log.Println("警告: 找不到.env文件")
	}

	// 初始化数据库连接
	if err := database.ConnDatabase(); err != nil {
		log.Fatalf("数据库连接失败: %v", err)
	}

	// 初始化Redis连接
	if err := database.InitRedis(); err != nil {
		log.Fatalf("Redis连接失败: %v", err)
	}

	fmt.Println("=== SQL性能优化测试 ===")
	fmt.Println()

	// 测试菜单查询性能
	testMenuPerformance()
	
	// 测试项目列表查询性能
	testProjectListPerformance()
	
	// 测试项目内容统计性能
	testProjectContentCountPerformance()
	
	// 测试缓存性能
	testCachePerformance()

	fmt.Println("\n=== 性能测试完成 ===")
}

// testMenuPerformance 测试菜单查询性能
func testMenuPerformance() {
	fmt.Println("📋 测试菜单查询性能...")
	
	// 测试1: 传统方式查询所有菜单（模拟N+1查询）
	start := time.Now()
	var menus []models.Menu
	database.DB.Find(&menus)
	
	// 模拟N+1查询 - 为每个菜单查询子菜单
	for _, menu := range menus {
		var children []models.Menu
		database.DB.Where("parent_id = ?", menu.ID).Find(&children)
	}
	oldWay := time.Since(start)
	
	// 测试2: 优化后的方式
	start = time.Now()
	_, err := models.GetMenuTreeFromDB(database.DB)
	if err != nil {
		log.Printf("优化查询失败: %v", err)
	}
	newWay := time.Since(start)
	
	// 测试3: 缓存方式
	start = time.Now()
	cacheService := utils.GetMenuCacheService()
	_, err = cacheService.GetMenuTreeFromCache()
	if err != nil {
		log.Printf("缓存查询失败: %v", err)
	}
	cacheWay := time.Since(start)
	
	fmt.Printf("  传统方式(N+1查询): %v\n", oldWay)
	fmt.Printf("  优化后方式: %v (提升 %.1fx)\n", newWay, float64(oldWay)/float64(newWay))
	fmt.Printf("  缓存方式: %v (提升 %.1fx)\n", cacheWay, float64(oldWay)/float64(cacheWay))
	fmt.Println()
}

// testProjectListPerformance 测试项目列表查询性能
func testProjectListPerformance() {
	fmt.Println("📊 测试项目列表查询性能...")
	
	// 获取一些项目用于测试
	var projects []models.Project
	database.DB.Limit(20).Find(&projects)
	
	if len(projects) == 0 {
		fmt.Println("  没有项目数据，跳过测试")
		return
	}
	
	// 测试1: 传统方式 - N+1查询
	start := time.Now()
	for _, project := range projects {
		var contentCount int64
		database.DB.Model(&models.ProjectContent{}).Where("project_id = ?", project.ID).Count(&contentCount)
	}
	oldWay := time.Since(start)
	
	// 测试2: 批量查询方式
	start = time.Now()
	var projectIDs []uint64
	for _, project := range projects {
		projectIDs = append(projectIDs, project.ID)
	}
	
	type ContentCountResult struct {
		ProjectID uint64 `json:"project_id"`
		Count     int64  `json:"count"`
	}
	var contentCounts []ContentCountResult
	database.DB.Model(&models.ProjectContent{}).
		Select("project_id, COUNT(*) as count").
		Where("project_id IN ?", projectIDs).
		Group("project_id").
		Scan(&contentCounts)
	newWay := time.Since(start)
	
	// 测试3: 缓存方式
	start = time.Now()
	cacheService := utils.GetProjectCacheService()
	_, err := cacheService.GetProjectContentCountsBatch(projectIDs)
	if err != nil {
		log.Printf("缓存查询失败: %v", err)
	}
	cacheWay := time.Since(start)
	
	fmt.Printf("  传统方式(N+1查询): %v\n", oldWay)
	fmt.Printf("  批量查询方式: %v (提升 %.1fx)\n", newWay, float64(oldWay)/float64(newWay))
	fmt.Printf("  缓存方式: %v (提升 %.1fx)\n", cacheWay, float64(oldWay)/float64(cacheWay))
	fmt.Println()
}

// testProjectContentCountPerformance 测试项目内容统计性能
func testProjectContentCountPerformance() {
	fmt.Println("📈 测试项目内容统计性能...")
	
	// 获取项目数量
	var projectCount int64
	database.DB.Model(&models.Project{}).Count(&projectCount)
	
	var contentCount int64
	database.DB.Model(&models.ProjectContent{}).Count(&contentCount)
	
	fmt.Printf("  项目总数: %d\n", projectCount)
	fmt.Printf("  内容总数: %d\n", contentCount)
	
	if projectCount == 0 {
		fmt.Println("  没有项目数据，跳过测试")
		return
	}
	
	// 测试缓存预热
	start := time.Now()
	cacheService := utils.GetProjectCacheService()
	err := cacheService.WarmupProjectContentCountCache(int(projectCount))
	if err != nil {
		log.Printf("缓存预热失败: %v", err)
	}
	warmupTime := time.Since(start)
	
	fmt.Printf("  缓存预热时间: %v\n", warmupTime)
	
	// 获取缓存统计
	stats, err := cacheService.GetCacheStats()
	if err != nil {
		log.Printf("获取缓存统计失败: %v", err)
	} else {
		fmt.Printf("  缓存统计: %+v\n", stats)
	}
	
	fmt.Println()
}

// testCachePerformance 测试缓存性能
func testCachePerformance() {
	fmt.Println("🚀 测试缓存性能...")
	
	// 测试菜单缓存命中率
	cacheService := utils.GetMenuCacheService()
	
	// 第一次查询（缓存未命中）
	start := time.Now()
	_, err := cacheService.GetMenuTreeFromCache()
	if err != nil {
		log.Printf("缓存查询失败: %v", err)
	}
	firstQuery := time.Since(start)
	
	// 第二次查询（缓存命中）
	start = time.Now()
	_, err = cacheService.GetMenuTreeFromCache()
	if err != nil {
		log.Printf("缓存查询失败: %v", err)
	}
	secondQuery := time.Since(start)
	
	fmt.Printf("  菜单缓存首次查询: %v\n", firstQuery)
	fmt.Printf("  菜单缓存命中查询: %v (提升 %.1fx)\n", secondQuery, float64(firstQuery)/float64(secondQuery))
	
	// 测试项目内容统计缓存
	var projects []models.Project
	database.DB.Limit(10).Find(&projects)
	
	if len(projects) > 0 {
		var projectIDs []uint64
		for _, project := range projects {
			projectIDs = append(projectIDs, project.ID)
		}
		
		projectCacheService := utils.GetProjectCacheService()
		
		// 清除缓存
		projectCacheService.InvalidateProjectContentCountBatch(projectIDs)
		
		// 第一次查询（缓存未命中）
		start = time.Now()
		_, err = projectCacheService.GetProjectContentCountsBatch(projectIDs)
		if err != nil {
			log.Printf("项目缓存查询失败: %v", err)
		}
		firstProjectQuery := time.Since(start)
		
		// 第二次查询（缓存命中）
		start = time.Now()
		_, err = projectCacheService.GetProjectContentCountsBatch(projectIDs)
		if err != nil {
			log.Printf("项目缓存查询失败: %v", err)
		}
		secondProjectQuery := time.Since(start)
		
		fmt.Printf("  项目统计缓存首次查询: %v\n", firstProjectQuery)
		fmt.Printf("  项目统计缓存命中查询: %v (提升 %.1fx)\n", secondProjectQuery, float64(firstProjectQuery)/float64(secondProjectQuery))
	}
	
	fmt.Println()
}
