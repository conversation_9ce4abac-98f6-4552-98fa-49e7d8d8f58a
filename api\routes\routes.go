package routes

import (
	"go-fiber-api/api/handlers"

	"github.com/gofiber/fiber/v2"
)

// SetupRoutes 设置所有API路由
func SetupRoutes(app *fiber.App) {
	// 首先设置Gateway路由（无/api前缀）
	SetupGatewayRoutes(app)

	// 设置WebSocket路由
	SetupWebSocketRoutes(app)

	// API路由组
	api := app.Group("/api")

	// --- 公开路由 ---
	api.Get("/health", func(c *fiber.Ctx) error {
		return c.JSON(fiber.Map{
			"status":  "success",
			"message": "系统正常运行",
		})
	})

	// 公开获取友情链接列表接口
	api.Get("/admin/friendly-list", handlers.GetPublicFriendlies)
	
	// 公开获取系统设置接口
	api.Get("/admin/settings/public", handlers.GetPublicSettings)

	// 认证路由 (不需要全局认证中间件) - 必须在管理员路由之前注册
	SetupAuthRoutes(api.Group("/admin")) // 挂载在 /api/admin/auth 下

	// 双因素认证路由 (需要认证中间件)
	SetupTwoFactorRoutes(api.Group("/admin")) // 挂载在 /api/admin/auth/2fa 下

	// --- 受保护的路由 ---
	// 管理员路由 (这里应用认证中间件) - 注意：这可能会影响 /admin/auth 路由
	SetupAdminRoutes(api)

	// 控制面板路由
	SetupDashboardRoutes(api.Group("/admin"))

	// 公告路由
	SetupArticleRoutes(api.Group("/admin"))

	// 项目路由
	SetupProjectRoutes(api.Group("/admin"))

	// 项目内容路由
	SetupProjectsContentRoutes(api.Group("/admin"))

	// 模块路由
	SetupModuleRoutes(api.Group("/admin"))

	// XSS模板路由
	SetupXssTemplateRoutes(api.Group("/admin"))

	// 域名路由
	SetupDomainRoutes(api.Group("/admin"))

	// 友情链接路由
	SetupFriendlyRoutes(api.Group("/admin"))

	// 邀请码路由
	SetupInvitationRoutes(api.Group("/admin"))

	// 网站设置路由
	SetupSettingsRoutes(api.Group("/admin"))

	// 角色管理路由
	SetupRoleRoutes(api.Group("/admin"))

	// 菜单管理路由
	SetupMenuRoutes(api.Group("/admin"))

	// 权限管理路由
	SetupPermissionRoutes(api.Group("/admin"))

	// 失败任务路由
	SetupFailedJobRoutes(api.Group("/admin"))

	// 任务路由
	SetupJobRoutes(api.Group("/admin"))

	// 邮件路由
	SetupEmailRoutes(api)

	// 队列路由
	SetupQueueRoutes(api)
}
