package processors

import (
	"context"
	"encoding/json"
	"fmt"
	"go-fiber-api/database"
	"go-fiber-api/utils/queue/tasks"
	"log"
	"time"

	"github.com/hibiken/asynq"
)

// NotificationProcessor 处理通知发送任务
func NotificationProcessor(ctx context.Context, t *asynq.Task) error {
	var payload tasks.NotificationPayload
	if err := json.Unmarshal(t.Payload(), &payload); err != nil {
		return fmt.Errorf("错误的任务负载: %v", err)
	}

	log.Printf("处理用户 %d 的通知: %s\n", payload.UserID, payload.Title)

	// 构建通知数据
	notification := map[string]interface{}{
		"user_id":    payload.UserID,
		"title":      payload.Title,
		"content":    payload.Content,
		"type":       payload.Type,
		"read":       false,
		"created_at": time.Now(),
	}

	// 将通知保存到数据库
	// 注意：这里假设使用GORM和database.DB连接
	if err := database.DB.Table("notifications").Create(notification).Error; err != nil {
		return fmt.Errorf("保存通知失败: %v", err)
	}

	// 可以在这里添加实时推送逻辑，如WebSocket或其他推送服务

	log.Printf("成功为用户 %d 创建通知\n", payload.UserID)
	return nil
} 