---
description: 
globs: 
alwaysApply: false
---
# API响应结构规范

本文档定义了Go Fiber API项目中使用的标准JSON响应结构。

## 响应结构

所有API响应都应遵循以下结构：

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {},
  "error": ""
}
```

### 字段说明

- **`code`** (int, 必选):
  - 业务状态码。
  - `0` 代表请求成功。
  - 非 `0` 代表请求失败，具体数值表示不同的错误类型（自定义）。
  - 建议使用HTTP状态码作为基础，如400, 401, 403, 404, 500等。

- **`message`** (string, 必选):
  - 用户可读的提示信息。
  - 对于成功请求，通常是"操作成功"或更具体的描述。
  - 对于失败请求，应提供清晰的错误原因。

- **`data`** (any, 可选):
  - 实际返回的数据负载。
  - 仅在请求成功(`code == 0`)时包含。
  - 可以是任何有效的JSON类型（对象、数组、字符串、数字、布尔值）。
  - 对于列表数据，建议使用分页结构，参考 [api/dto/admin_user_dto.go](mdc:api/dto/admin_user_dto.go) 中的 `PaginatedResponse`。

- **`error`** (string, 可选):
  - 详细的错误信息，主要用于开发和调试。
  - 仅在请求失败(`code != 0`)时且配置允许（例如开发环境）时包含。
  - **生产环境中不应暴露详细的系统错误信息。**

## 使用方法

推荐使用 [utils/response.go](mdc:utils/response.go) 中提供的工具函数来生成标准响应：

- **`utils.Success(c, message, data)`**: 返回成功的响应 (code=0)。
- **`utils.Fail(c, statusCode, errorCode, message, err)`**: 返回失败的响应 (code != 0)。
- **`utils.BadRequest(c, message, err)`**: 返回400错误。
- **`utils.Unauthorized(c, message, err)`**: 返回401错误。
- **`utils.Forbidden(c, message, err)`**: 返回403错误。
- **`utils.NotFound(c, message)`**: 返回404错误。
- **`utils.ServerError(c, message, err)`**: 返回500错误。

## 示例

### 成功响应 (获取用户列表)

```json
{
  "code": 0,
  "message": "获取用户列表成功",
  "data": {
    "items": [
      {
        "id": 1,
        "username": "admin",
        "name": "管理员",
        ...
      }
    ],
    "total": 10,
    "page": 1,
    "page_size": 10,
    "total_pages": 1
  }
}
```

### 失败响应 (资源未找到)

```json
{
  "code": 404,
  "message": "用户不存在",
  "error": "record not found" // 可选的详细错误
}
```

### 失败响应 (无效请求)

```json
{
  "code": 400,
  "message": "用户名已存在",
  "error": "duplicate entry for key 'username'" // 可选的详细错误
}
```

统一的响应结构有助于前端或其他服务消费者更容易地处理API返回结果。

