package handlers

import (
	"go-fiber-api/api/dto"
	"go-fiber-api/database"
	"go-fiber-api/models"
	"go-fiber-api/utils"
	"strconv"
	"time"

	"github.com/gofiber/fiber/v2"
)

// GetDashboardData 获取控制面板数据
// @Summary 获取控制面板数据
// @Description 获取系统概览数据，包括用户数、角色数、权限数等
// @Tags 控制面板
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} dto.StandardResponse{data=dto.DashboardDataResponse}
// @Router /admin/dashboard [get]
func GetDashboardData(c *fiber.Ctx) error {
	db := database.DB

	// 获取用户数量
	var userCount int64
	db.Model(&models.AdminUser{}).Count(&userCount)

	// 获取角色数量
	var roleCount int64
	db.Model(&models.Role{}).Count(&roleCount)

	// 获取权限数量
	var permissionCount int64
	db.Model(&models.Permission{}).Count(&permissionCount)

	// 获取项目数量
	var projectCount int64
	db.Model(&models.Project{}).Count(&projectCount)

	// 获取菜单数量
	var menuCount int64
	db.Model(&models.Menu{}).Count(&menuCount)

	// 今日注册用户数
	var todayUserCount int64
	today := time.Now().Format("2006-01-02")
	db.Model(&models.AdminUser{}).Where("DATE(created_at) = ?", today).Count(&todayUserCount)

	// 今日发布项目数
	var todayProjectCount int64
	db.Model(&models.Project{}).Where("DATE(created_at) = ?", today).Count(&todayProjectCount)

	// 构建响应数据
	response := dto.DashboardDataResponse{
		UserCount:         userCount,
		RoleCount:         roleCount,
		PermissionCount:   permissionCount,
		ProjectCount:      projectCount,
		MenuCount:         menuCount,
		TodayUserCount:    todayUserCount,
		TodayProjectCount: todayProjectCount,
	}

	return utils.Success(c, "获取控制面板数据成功", response)
}

// GetRecentOperationLogs 获取最近操作日志
// @Summary 获取最近操作日志
// @Description 获取系统中的最近操作日志
// @Tags 控制面板
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param limit query int false "限制数量，默认为10"
// @Success 200 {object} dto.StandardResponse{data=[]dto.OperationLogResponse}
// @Router /admin/dashboard/logs [get]
func GetRecentOperationLogs(c *fiber.Ctx) error {
	db := database.DB

	// 获取limit参数，默认为10
	limitStr := c.Query("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10
	}

	// 获取最近的操作日志
	var logs []models.AdminOperationLog
	db.Preload("User").Order("created_at DESC").Limit(limit).Find(&logs)

	// 转换为响应DTO
	var logResponses []dto.OperationLogResponse
	for _, log := range logs {
		username := ""
		if log.User != nil {
			username = log.User.Username
		}

		logResponses = append(logResponses, dto.OperationLogResponse{
			ID:        log.ID,
			UserID:    log.UserID,
			Username:  username,
			Path:      log.Path,
			Method:    log.Method,
			IP:        log.IP,
			Input:     log.Input,
			CreatedAt: log.CreatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	return utils.Success(c, "获取最近操作日志成功", logResponses)
}

// GetSystemInfo 获取系统信息
// @Summary 获取系统信息
// @Description 获取系统运行信息，包括服务器状态、数据库状态等
// @Tags 控制面板
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} dto.StandardResponse{data=dto.SystemInfoResponse}
// @Router /admin/dashboard/system [get]
func GetSystemInfo(c *fiber.Ctx) error {
	// 获取系统版本信息
	version := "1.0.0"

	// 获取系统运行时间
	startTime := time.Now().AddDate(0, 0, -30) // 模拟系统启动时间
	uptime := time.Since(startTime).String()

	// 获取Go版本
	goVersion := "1.20"

	// 构建响应数据
	response := dto.SystemInfoResponse{
		Version:         version,
		Uptime:          uptime,
		GoVersion:       goVersion,
		ServerTime:      time.Now().Format("2006-01-02 15:04:05"),
		DatabaseVersion: "MySQL 8.0",
	}

	return utils.Success(c, "获取系统信息成功", response)
} 