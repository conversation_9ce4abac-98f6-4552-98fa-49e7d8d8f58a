package queue

import (
	"go-fiber-api/utils/email"
	"go-fiber-api/utils/queue/tasks"
)

// EnqueueEmailTask 将邮件任务入队（异步发送）
func EnqueueEmailTask(data email.EmailData) error {
	client := GetClient()
	// 渲染正文
	body := email.GetEmailBody(data)
	// 只支持单收件人（如需群发可扩展）
	to := ""
	if len(data.To) > 0 {
		to = data.To[0]
	}
	// 构建任务
	task, err := tasks.NewEmailTask(to, data.Subject, body, data.IsHTML)
	if err != nil {
		return err
	}
	// 入队
	_, err = client.Enqueue(task)
	return err
}
