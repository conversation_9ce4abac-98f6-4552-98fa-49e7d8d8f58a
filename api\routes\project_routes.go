package routes

import (
	"go-fiber-api/api/handlers"
	"go-fiber-api/api/middleware"
	"go-fiber-api/utils"

	"github.com/gofiber/fiber/v2"
)

// SetupProjectRoutes 设置项目相关路由
func SetupProjectRoutes(router fiber.Router) {
	project := router.Group("/project")
	
	// 添加认证中间件
	project.Use(utils.RequireAuthentication)
	
	// 项目管理路由
	project.Get("/my", middleware.RequirePermission("project.view"), handlers.GetProjects)
	project.Post("/my", middleware.RequirePermission("project.create"), handlers.CreateProject)
	
	// 特殊路由 - 必须放在带参数的路由之前
	project.Get("/my/preview", middleware.RequirePermission("project.view"), handlers.Preview)
	
	// 带参数的路由
	project.Get("/my/:id", middleware.RequirePermission("project.view"), handlers.GetProject)
	project.Put("/my/:id", middleware.RequirePermission("project.edit"), handlers.UpdateProject)
	project.Put("/my/:id/toggle", middleware.RequirePermission("project.edit"), handlers.ToggleProjectState)
	project.Delete("/my/:id", middleware.RequirePermission("project.delete"), handlers.DeleteProject)
	// 添加恢复项目路由
	project.Put("/restore/:id", middleware.RequirePermission("project.edit"), handlers.RestoreProject)

	// 其他特殊路由
	project.Get("/viewcode/:projectid", middleware.RequirePermission("project.view"), handlers.ViewCode)
} 