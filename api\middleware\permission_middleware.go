package middleware

import (
	"go-fiber-api/utils"
	"strings"

	"github.com/gofiber/fiber/v2"
)

// 权限中间件类型常量
const (
	PERMISSION_TYPE_SLUG   = "slug"   // 按权限标识检查
	PERMISSION_TYPE_PATH   = "path"   // 按路径检查
	PERMISSION_TYPE_ROLE   = "role"   // 按角色检查
	PERMISSION_TYPE_MENU   = "menu"   // 按菜单检查
	PERMISSION_TYPE_CUSTOM = "custom" // 自定义检查
)

// RequirePermission 检查特定权限标识的中间件
// 使用方式: app.Get("/admin/permissions", middleware.RequirePermission("permission.view"), handlers.GetPermissions)
func RequirePermission(permissionSlug string) fiber.Handler {
	return func(c *fiber.Ctx) error {
		if !utils.CheckPermissionBySlug(c, permissionSlug) {
			return utils.Forbidden(c, "您没有权限执行此操作", nil)
		}
		return c.Next()
	}
}

// RequireRole 检查特定角色的中间件
// 使用方式: app.Get("/admin/settings", middleware.RequireRole("admin"), handlers.GetSettings)
func RequireRole(roleName string) fiber.Handler {
	return func(c *fiber.Ctx) error {
		if !utils.HasUserRole(c, roleName) {
			return utils.Forbidden(c, "您没有所需的角色权限", nil)
		}
		return c.Next()
	}
}

// CheckPathPermission 检查路径权限的中间件
// 使用方式: app.Get("/admin/custom/*", middleware.CheckPathPermission(), handlers.CustomHandler)
func CheckPathPermission() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// 获取当前请求路径和方法
		path := c.Path()
		method := c.Method()

		// 跳过公开路径
		if isPublicPath(path) {
			return c.Next()
		}

		// 检查是否已登录
		userID := utils.GetUserIDFromContext(c)
		if userID == 0 {
			return utils.Unauthorized(c, "未登录或登录已过期", nil)
		}

		// 管理员拥有所有权限
		if utils.IsAdmin(c) {
			return c.Next()
		}

		// 检查用户是否有访问权限
		if utils.CheckPermission(c, path, method) {
			return c.Next()
		}

		return utils.Forbidden(c, "您没有权限访问该资源", nil)
	}
}

// isPublicPath 检查路径是否为公开路径
func isPublicPath(path string) bool {
	publicPaths := []string{
		"/api/auth/login",
		"/api/auth/register",
		"/api/auth/forgot-password",
		"/api/auth/reset-password",
		"/swagger",
		"/api-docs",
	}

	for _, publicPath := range publicPaths {
		if strings.HasPrefix(path, publicPath) {
			return true
		}
	}
	return false
}

// CheckMenuAccess 检查菜单访问权限的中间件
// 使用方式: app.Get("/admin/menu/tree", middleware.CheckMenuAccess(), handlers.GetUserMenuTree)
func CheckMenuAccess() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// 如果是管理员直接放行
		if utils.IsAdmin(c) {
			return c.Next()
		}

		userID := utils.GetUserIDFromContext(c)
		if userID == 0 {
			return utils.Unauthorized(c, "未登录或登录已过期", nil)
		}

		// 对于菜单树API的特殊处理
		if c.Path() == "/admin/menu/tree" {
			// 所有已认证用户都可以访问此API，它会根据用户权限过滤菜单
			return c.Next()
		}

		// 其他菜单管理功能需要特定权限
		if !utils.CheckPermissionBySlug(c, "menu.view") {
			return utils.Forbidden(c, "您没有权限访问菜单管理", nil)
		}

		return c.Next()
	}
}

// Auth 验证用户是否已登录的中间件
// 使用方式: app.Use("/admin/*", middleware.Auth())
func Auth() fiber.Handler {
	return func(c *fiber.Ctx) error {
		if utils.GetUserIDFromContext(c) == 0 {
			return utils.Unauthorized(c, "请先登录", nil)
		}
		return c.Next()
	}
}

// AdminOnly 仅管理员可访问的中间件
// 使用方式: app.Use("/admin/sensitive/*", middleware.AdminOnly())
func AdminOnly() fiber.Handler {
	return func(c *fiber.Ctx) error {
		if !utils.IsAdmin(c) {
			return utils.Forbidden(c, "仅管理员可访问", nil)
		}
		return c.Next()
	}
} 