package handlers

import (
	"fmt"
	"go-fiber-api/api/dto"
	"go-fiber-api/database"
	"go-fiber-api/models"
	"go-fiber-api/utils"
	"log"
	"strconv"
	"time"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

// GetProjectContents 获取项目内容列表
// @Summary 获取项目内容列表
// @Description 获取项目内容列表，支持分页
// @Tags 项目内容管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码，默认为1"
// @Param page_size query int false "每页条数，默认为10"
// @Param project_id query int false "项目ID过滤"
// @Param with_deleted query bool false "是否包含已删除记录"
// @Success 200 {object} dto.StandardResponse{data=dto.PaginatedResponse{items=[]dto.ProjectContentListItem}}
// @Router /admin/projectscontent/index [get]
func GetProjectContents(c *fiber.Ctx) error {
	db := database.DB.Model(&models.ProjectContent{})
	
	// 获取分页参数
	page, _ := strconv.Atoi(c.Query("page", "1"))
	pageSize, _ := strconv.Atoi(c.Query("page_size", "10"))
	projectID := c.Query("project_id", "")
	withDeletedStr := c.Query("with_deleted", "false")
	withDeleted := withDeletedStr == "true"
	
	// 详细日志
	log.Printf("获取项目内容列表 - 参数: page=%d, page_size=%d, project_id=%s, with_deleted=%s (%t)", 
		page, pageSize, projectID, withDeletedStr, withDeleted)
	
	// 构建查询
	var query *gorm.DB
	isAdmin := utils.IsAdmin(c)
	
	log.Printf("用户是否为管理员: %t", isAdmin)
	
	// 管理员可以查看已删除的记录
	if withDeleted && isAdmin {
		log.Println("使用 Unscoped() 查询包含已删除的记录")
		query = db.Unscoped() // 包括已删除的记录
	} else {
		log.Println("使用普通查询不包含已删除的记录")
		query = db
	}
	
	// 应用过滤
	if projectID != "" {
		projectIDInt, _ := strconv.Atoi(projectID)
		query = query.Where("project_id = ?", projectIDInt)
	}

	// 构建查询，根据用户角色过滤项目列表
	// 普通用户只能查看自己的项目，管理员可以查看所有项目
	if !isAdmin {
		userID := utils.GetUserIDFromContext(c)
		log.Printf("普通用户 %d 仅查看自己的内容", userID)
		query = query.Where("user_id = ?", userID)
	} else {
		log.Println("管理员查看所有内容")
	}
	
	// 计算总数
	var total int64
	query.Count(&total)
	log.Printf("符合条件的内容总数: %d", total)
	
	// 获取分页数据
	var contents []models.ProjectContent
	query.Order("cid DESC").
		Offset((page - 1) * pageSize).
		Limit(pageSize).
		Find(&contents)
	log.Printf("查询到的内容数量: %d", len(contents))
	
	// 转换为DTO
	var contentDTOs []dto.ProjectContentListItem
	for _, content := range contents {
		// 构建源码文件和屏幕截图的URL路径
		// 构建静态资源路径
		uploadDate := time.Now().Format("2006-01-02")
		sourceFileURL := "/static/uploads/" + uploadDate + "/" + fmt.Sprintf("%d", content.ProjectID) + "/" + content.Hash + ".txt"
		screenshotURL := "/static/uploads/" + uploadDate + "/" + fmt.Sprintf("%d", content.ProjectID) + "/" + content.Hash + ".png"
		
		deleted := false
		if !content.DeletedAt.Time.IsZero() {
			deleted = true
			log.Printf("内容 %d 已被删除 (DeletedAt=%v)", content.CID, content.DeletedAt)
		}
		
		contentDTOs = append(contentDTOs, dto.ProjectContentListItem{
			CID:        content.CID,
			ProjectID:  content.ProjectID,
			Domain:     content.Domain,
			Hash:       content.Hash,
			Content:    content.Content,
			Server:     content.Server,
			SourceFile: sourceFileURL,
			Screenshot: screenshotURL,
			State:      content.State,
			Deleted:    deleted,
			CreatedAt:  content.CreatedAt.Format("2006-01-02 15:04:05"),
		})
	}
	
	// 返回分页响应
	return utils.SuccessPaginated(c, "获取项目内容列表成功", contentDTOs, total, page, pageSize)
}

// GetProjectContent 获取项目内容详情
// @Summary 获取项目内容详情
// @Description 根据ID获取项目内容详情
// @Tags 项目内容管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "项目内容ID"
// @Success 200 {object} dto.StandardResponse{data=dto.ProjectContentResponse}
// @Router /admin/projectscontent/index/{id} [get]
func GetProjectContent(c *fiber.Ctx) error {
	db := database.DB.Model(&models.ProjectContent{})
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的内容ID", err)
	}
	
	var content models.ProjectContent
	if err := db.First(&content, "cid = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "项目内容不存在")
		}
		return utils.ServerError(c, "获取项目内容失败", err)
	}
	
	// 转换为DTO
	// 构建源码文件和屏幕截图的URL路径
	// 构建静态资源路径
	uploadDate := time.Now().Format("2006-01-02")
	sourceFileURL := "/static/uploads/" + uploadDate + "/" + fmt.Sprintf("%d", content.ProjectID) + "/" + content.Hash + ".txt"
	screenshotURL := "/static/uploads/" + uploadDate + "/" + fmt.Sprintf("%d", content.ProjectID) + "/" + content.Hash + ".png"
	
	contentDTO := dto.ProjectContentResponse{
		CID:        content.CID,
		ProjectID:  content.ProjectID,
		UserID:     content.UserID,
		Content:    content.Content,
		Server:     content.Server,
		Domain:     content.Domain,
		Hash:       content.Hash,
		SourceFile: sourceFileURL,
		Screenshot: screenshotURL,
		State:      content.State,
		CreatedAt:  content.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:  content.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
	
	return utils.Success(c, "获取项目内容成功", contentDTO)
}

// CreateProjectContent 创建项目内容
// @Summary 创建项目内容
// @Description 创建新的项目内容
// @Tags 项目内容管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param content body dto.ProjectContentRequest true "项目内容信息"
// @Success 200 {object} dto.StandardResponse{data=dto.ProjectContentResponse}
// @Router /admin/projectscontent/index [post]
func CreateProjectContent(c *fiber.Ctx) error {
	db := database.DB.Model(&models.ProjectContent{})
	
	// 解析请求体
	var req dto.ProjectContentRequest
	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}
	
	// 验证请求数据
	if err := utils.ValidateStruct(req); err != nil {
		return utils.BadRequest(c, "数据验证失败", err)
	}
	
	// 获取当前用户ID
	userID := utils.GetUserIDFromContext(c)
	
	// 查询项目
	var project models.Project
	if err := database.DB.First(&project, req.ProjectID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "项目不存在")
		}
		return utils.ServerError(c, "获取项目失败", err)
	}
	
	// 创建新项目内容
	content := models.ProjectContent{
		ProjectID: req.ProjectID,
		UserID:    userID,
		Content:   req.Content,
		Server:    req.Server,
		Domain:    req.Domain,
		Hash:      req.Hash,
		State:     req.State,
	}
	
	if err := db.Create(&content).Error; err != nil {
		return utils.ServerError(c, "创建项目内容失败", err)
	}
	
	// 更新项目的IsNewRecord标志
	db.Model(&project).Update("is_new_record", true)
	
	// 转换为DTO
	// 构建源码文件和屏幕截图的URL路径
	// 构建静态资源路径
	uploadDate := time.Now().Format("2006-01-02")
	sourceFileURL := "/static/uploads/" + uploadDate + "/" + fmt.Sprintf("%d", content.ProjectID) + "/" + content.Hash + ".txt"
	screenshotURL := "/static/uploads/" + uploadDate + "/" + fmt.Sprintf("%d", content.ProjectID) + "/" + content.Hash + ".png"
	
	contentDTO := dto.ProjectContentResponse{
		CID:        content.CID,
		ProjectID:  content.ProjectID,
		UserID:     content.UserID,
		Content:    content.Content,
		Server:     content.Server,
		Domain:     content.Domain,
		Hash:       content.Hash,
		SourceFile: sourceFileURL,
		Screenshot: screenshotURL,
		State:      content.State,
		CreatedAt:  content.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:  content.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
	
	return utils.Success(c, "创建项目内容成功", contentDTO)
}

// UpdateProjectContent 更新项目内容
// @Summary 更新项目内容
// @Description 更新现有项目内容
// @Tags 项目内容管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "内容ID"
// @Param content body dto.ProjectContentUpdateRequest true "项目内容更新信息"
// @Success 200 {object} dto.StandardResponse{data=dto.ProjectContentResponse}
// @Router /admin/projectscontent/index/{id} [put]
func UpdateProjectContent(c *fiber.Ctx) error {
	db := database.DB.Model(&models.ProjectContent{})
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的内容ID", err)
	}
	
	// 解析请求体
	var req dto.ProjectContentUpdateRequest
	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}
	
	// 验证请求数据
	if err := utils.ValidateStruct(req); err != nil {
		return utils.BadRequest(c, "数据验证失败", err)
	}
	
	// 查找项目内容
	var content models.ProjectContent
	if err := db.First(&content, "cid = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "项目内容不存在")
		}
		return utils.ServerError(c, "获取项目内容失败", err)
	}
	
	// 更新字段
	updates := make(map[string]interface{})
	if req.Content != nil {
		updates["content"] = *req.Content
	}
	if req.Server != nil {
		updates["server"] = *req.Server
	}
	if req.Domain != nil {
		updates["domain"] = *req.Domain
	}
	if req.Hash != nil {
		updates["hash"] = *req.Hash
	}
	if req.State != nil {
		updates["state"] = *req.State
	}
	
	// 添加更新时间
	updates["updated_at"] = time.Now()
	
	// 执行更新
	if err := db.Model(&content).Updates(updates).Error; err != nil {
		return utils.ServerError(c, "更新项目内容失败", err)
	}
	
	// 重新获取更新后的内容
	if err := db.First(&content, "cid = ?", id).Error; err != nil {
		return utils.ServerError(c, "获取更新后的项目内容失败", err)
	}
	
	// 转换为DTO
	// 构建源码文件和屏幕截图的URL路径
	// 构建静态资源路径
	uploadDate := time.Now().Format("2006-01-02")
	sourceFileURL := "/static/uploads/" + uploadDate + "/" + fmt.Sprintf("%d", content.ProjectID) + "/" + content.Hash + ".txt"
	screenshotURL := "/static/uploads/" + uploadDate + "/" + fmt.Sprintf("%d", content.ProjectID) + "/" + content.Hash + ".png"
	
	contentDTO := dto.ProjectContentResponse{
		CID:        content.CID,
		ProjectID:  content.ProjectID,
		UserID:     content.UserID,
		Content:    content.Content,
		Server:     content.Server,
		Domain:     content.Domain,
		Hash:       content.Hash,
		SourceFile: sourceFileURL,
		Screenshot: screenshotURL,
		State:      content.State,
		CreatedAt:  content.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:  content.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
	
	return utils.Success(c, "更新项目内容成功", contentDTO)
}

// DeleteProjectContent 删除项目内容
// @Summary 删除项目内容
// @Description 根据ID删除项目内容
// @Tags 项目内容管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "内容ID"
// @Success 200 {object} dto.StandardResponse
// @Router /admin/projectscontent/index/{id} [delete]
func DeleteProjectContent(c *fiber.Ctx) error {
	db := database.DB.Model(&models.ProjectContent{})
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的内容ID", err)
	}
	
	// 查找项目内容
	var content models.ProjectContent
	if err := db.First(&content, "cid = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "项目内容不存在")
		}
		return utils.ServerError(c, "获取项目内容失败", err)
	}
	
	// 执行软删除
	if err := db.Delete(&content).Error; err != nil {
		return utils.ServerError(c, "删除项目内容失败", err)
	}
	
	return utils.Success(c, "删除项目内容成功", nil)
}

// GetProjectContentDetail 获取项目内容详情（特定路由）
// @Summary 获取项目内容详情
// @Description 根据用户ID和项目ID获取项目内容详情
// @Tags 项目内容管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param userid path int true "用户ID"
// @Param projectid path int true "项目ID"
// @Success 200 {object} dto.StandardResponse{data=dto.ProjectContentResponse}
// @Router /admin/project/my/{userid}/{projectid} [get]
func GetProjectContentDetail(c *fiber.Ctx) error {
	db := database.DB.Model(&models.ProjectContent{})
	userID, err := strconv.Atoi(c.Params("userid"))
	if err != nil {
		return utils.BadRequest(c, "无效的用户ID", err)
	}
	
	projectID, err := strconv.Atoi(c.Params("projectid"))
	if err != nil {
		return utils.BadRequest(c, "无效的项目ID", err)
	}
	
	// 查找项目内容
	var content models.ProjectContent
	if err := db.Where("user_id = ? AND project_id = ?", userID, projectID).
		Order("cid DESC").
		First(&content).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "项目内容不存在")
		}
		return utils.ServerError(c, "获取项目内容失败", err)
	}
	
	// 转换为DTO
	// 构建源码文件和屏幕截图的URL路径
	// 构建静态资源路径
	uploadDate := time.Now().Format("2006-01-02")
	sourceFileURL := "/static/uploads/" + uploadDate + "/" + fmt.Sprintf("%d", content.ProjectID) + "/" + content.Hash + ".txt"
	screenshotURL := "/static/uploads/" + uploadDate + "/" + fmt.Sprintf("%d", content.ProjectID) + "/" + content.Hash + ".png"
	
	contentDTO := dto.ProjectContentResponse{
		CID:        content.CID,
		ProjectID:  content.ProjectID,
		UserID:     content.UserID,
		Content:    content.Content,
		Server:     content.Server,
		Domain:     content.Domain,
		Hash:       content.Hash,
		SourceFile: sourceFileURL,
		Screenshot: screenshotURL,
		State:      content.State,
		CreatedAt:  content.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:  content.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
	
	return utils.Success(c, "获取项目内容成功", contentDTO)
}

// RemoveProjectContent 删除项目内容（特定路由）
// @Summary 删除项目内容
// @Description 根据用户ID和项目ID删除项目内容
// @Tags 项目内容管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param userid path int true "用户ID"
// @Param projectid path int true "项目ID"
// @Success 200 {object} dto.StandardResponse
// @Router /admin/project/my/{userid}/{projectid} [delete]
func RemoveProjectContent(c *fiber.Ctx) error {
	db := database.DB.Model(&models.ProjectContent{})
	userID, err := strconv.Atoi(c.Params("userid"))
	if err != nil {
		return utils.BadRequest(c, "无效的用户ID", err)
	}
	
	projectID, err := strconv.Atoi(c.Params("projectid"))
	if err != nil {
		return utils.BadRequest(c, "无效的项目ID", err)
	}
	
	// 查找项目内容
	var content models.ProjectContent
	if err := db.Where("user_id = ? AND project_id = ?", userID, projectID).
		First(&content).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "项目内容不存在")
		}
		return utils.ServerError(c, "获取项目内容失败", err)
	}
	
	// 执行软删除
	if err := db.Delete(&content).Error; err != nil {
		return utils.ServerError(c, "删除项目内容失败", err)
	}
	
	return utils.Success(c, "删除项目内容成功", nil)
}

// RestoreProjectContent 恢复已删除的项目内容
// @Summary 恢复已删除的项目内容
// @Description 根据ID恢复被软删除的项目内容
// @Tags 项目内容管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "内容ID"
// @Success 200 {object} dto.StandardResponse
// @Router /admin/projectscontent/restore/{id} [put]
func RestoreProjectContent(c *fiber.Ctx) error {
	db := database.DB.Model(&models.ProjectContent{})
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的内容ID", err)
	}
	
	// 执行恢复操作
	// Unscoped用于查询包括已删除记录
	var content models.ProjectContent
	result := db.Unscoped().Where("cid = ?", id).First(&content)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "项目内容不存在")
		}
		return utils.ServerError(c, "获取项目内容失败", result.Error)
	}
	
	// 检查记录是否已被删除
	if content.DeletedAt.Time.IsZero() {
		return utils.BadRequest(c, "此项目内容未被删除", nil)
	}
	
	// 执行恢复
	if err := db.Unscoped().Model(&models.ProjectContent{}).Where("cid = ?", id).Update("deleted_at", nil).Error; err != nil {
		return utils.ServerError(c, "恢复项目内容失败", err)
	}
	
	return utils.Success(c, "恢复项目内容成功", nil)
} 