package models

import (
	"time"

	"gorm.io/gorm"
)

// Friendly 友情链接模型
type Friendly struct {
	ID        uint64         `json:"id" gorm:"primaryKey;type:int unsigned"`
	Name      string         `json:"name" gorm:"not null;size:255;default:'128';comment:名称"`
	Href      string         `json:"href" gorm:"not null;size:255;comment:链接地址"`
	Thumb     string         `json:"thumb" gorm:"size:1024;comment:图片"`
	Type      string         `json:"type" gorm:"not null;type:varchar(2);default:'10';comment:类型 10 过滤域名/20 切换使用的域名"`
	State     int            `json:"state" gorm:"not null;default:10;comment:状态"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName 指定表名
func (Friendly) TableName() string {
	return "friendly"
} 