package models

import (
	"time"

	"gorm.io/gorm"
)

// Article 公告模型
type Article struct {
	ID          uint64         `json:"id" gorm:"primaryKey;type:int unsigned;comment:公告ID"`
	Title       string         `json:"title" gorm:"not null;size:255;comment:公告标题"`
	Description string         `json:"description" gorm:"type:text;comment:公告内容"`
	Status      int8           `json:"status" gorm:"type:tinyint;default:1;comment:状态 1-启用 0-禁用"`
	Author      string         `json:"author" gorm:"size:100;comment:作者"`
	CreatedBy   uint64         `json:"created_by" gorm:"type:bigint unsigned;comment:创建人ID"`
	UpdatedBy   uint64         `json:"updated_by" gorm:"type:bigint unsigned;comment:更新人ID"`
	CreatedAt   time.Time      `json:"created_at" gorm:"type:datetime(3);comment:创建时间"`
	UpdatedAt   time.Time      `json:"updated_at" gorm:"type:datetime(3);comment:更新时间"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index:idx_articles_deleted_at;type:datetime(3);comment:删除时间"`
}

// TableName 指定表名
func (Article) TableName() string {
	return "articles"
}
