package processors

import (
	"context"
	"encoding/json"
	"fmt"
	"go-fiber-api/utils/email"
	"go-fiber-api/utils/queue/tasks"
	"log"

	"github.com/hibiken/asynq"
)

// EmailProcessor 处理邮件发送任务
func EmailProcessor(ctx context.Context, t *asynq.Task) error {
	var payload tasks.EmailPayload
	if err := json.Unmarshal(t.Payload(), &payload); err != nil {
		return fmt.Errorf("错误的任务负载: %v", err)
	}

	log.Printf("发送邮件到 %s: %s\n", payload.To, payload.Subject)

	// 使用项目中的邮件发送工具
	err := email.SendEmail(email.EmailData{
		To:      payload.To,
		Subject: payload.Subject,
		Body:    payload.Body,
		IsHTML:  payload.IsHTML,
	})
	if err != nil {
		return fmt.Errorf("邮件发送失败: %v", err)
	}

	log.Printf("成功发送邮件到 %s\n", payload.To)
	return nil
} 