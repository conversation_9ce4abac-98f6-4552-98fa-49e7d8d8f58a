package models

import (
	"time"

	"gorm.io/gorm"
)

// Invitation 邀请码模型
type Invitation struct {
	ID          uint64         `json:"id" gorm:"primaryKey;type:int unsigned"`
	Code        string         `json:"code" gorm:"type:varchar(32);not null;default:'';comment:邀请码"`
	Description string         `json:"description" gorm:"type:varchar(500);default:'';comment:邀请码描述"`
	UserID      uint64         `json:"user_id" gorm:"type:bigint unsigned;default:0;comment:用户ID"`
	UsedCount   uint           `json:"used_count" gorm:"type:int unsigned;default:0;comment:使用次数"`
	Limit       uint           `json:"limit" gorm:"type:int unsigned;default:0;comment:使用次数限制，0表示不限制"`
	UsedAt      *time.Time     `json:"used_at" gorm:"comment:使用时间"`
	ExpiredAt   *time.Time     `json:"expired_at" gorm:"comment:过期时间"`
	State       int8           `json:"state" gorm:"type:tinyint(1);comment:状态"`
	CreatedAt   time.Time      `json:"created_at" gorm:"comment:创建时间"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName 指定表名
func (Invitation) TableName() string {
	return "invitation"
}

// IsExpired 检查邀请码是否过期
func (i *Invitation) IsExpired() bool {
	if i.ExpiredAt == nil {
		return false
	}
	return time.Now().After(*i.ExpiredAt)
} 