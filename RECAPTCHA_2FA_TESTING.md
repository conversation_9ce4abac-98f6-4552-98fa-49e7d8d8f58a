# reCAPTCHA v2 和 2FA 强制绑定功能测试指南

## 功能概述

本项目已成功集成以下功能：
1. **Google reCAPTCHA v2 三层验证**：图形验证码 → reCAPTCHA → 用户名密码验证
2. **2FA 强制绑定**：新用户首次登录后必须设置 Google Authenticator
3. **完善的错误处理**：用户友好的错误提示和交互体验

## 配置要求

### 1. 后端配置 (.env 文件)

```env
# Google reCAPTCHA v2 配置
RECAPTCHA_ENABLED=true
RECAPTCHA_SITE_KEY=your_recaptcha_site_key_here
RECAPTCHA_SECRET_KEY=your_recaptcha_secret_key_here
```

### 2. 获取 reCAPTCHA 密钥

1. 访问 [Google reCAPTCHA 控制台](https://www.google.com/recaptcha/admin)
2. 创建新站点，选择 reCAPTCHA v2 类型
3. 添加域名（开发环境可以使用 localhost）
4. 获取站点密钥（Site Key）和密钥（Secret Key）

### 3. 数据库迁移

确保用户表包含以下 2FA 相关字段：
```sql
ALTER TABLE admin_users ADD COLUMN two_factor_enabled BOOLEAN DEFAULT FALSE;
ALTER TABLE admin_users ADD COLUMN two_factor_secret VARCHAR(255);
ALTER TABLE admin_users ADD COLUMN two_factor_setup_at TIMESTAMP NULL;
```

## 测试用例

### 测试用例 1: reCAPTCHA 配置验证

**目标**：验证 reCAPTCHA 配置是否正确加载

**步骤**：
1. 启动后端服务
2. 访问 `GET /api/admin/auth/recaptcha-config`
3. 验证响应包含正确的配置信息

**期望结果**：
```json
{
  "success": true,
  "message": "reCAPTCHA 配置获取成功",
  "data": {
    "enabled": true,
    "site_key": "your_site_key"
  }
}
```

### 测试用例 2: 三层验证逻辑

**目标**：验证登录时的三层验证顺序

**步骤**：
1. 访问登录页面
2. 输入错误的图形验证码 → 应该提示"图形验证码错误"
3. 输入正确的图形验证码，不完成 reCAPTCHA → 应该提示"请完成 reCAPTCHA 验证"
4. 完成 reCAPTCHA，输入错误的用户名密码 → 应该提示"用户名或密码错误"
5. 输入正确的用户名密码 → 登录成功

**期望结果**：
- 每一层验证失败时都有相应的错误提示
- 验证失败后图形验证码和 reCAPTCHA 都会重置
- 只有三层验证都通过才能登录成功

### 测试用例 3: 新用户 2FA 强制绑定

**目标**：验证新用户首次登录后的 2FA 强制绑定流程

**步骤**：
1. 创建新用户账号（注册或管理员创建）
2. 使用新账号首次登录
3. 验证登录成功后是否自动跳转到 2FA 设置页面
4. 完成 2FA 设置流程
5. 验证设置完成后可以正常访问系统

**期望结果**：
- 新用户首次登录后自动跳转到 `/2fa/setup`
- 2FA 设置页面显示正确的步骤指引
- 设置完成后可以正常使用系统功能

### 测试用例 4: 2FA 设置流程

**目标**：验证 2FA 设置的完整流程

**步骤**：
1. 访问 2FA 设置页面
2. 点击"生成密钥"按钮
3. 验证二维码和密钥是否正确显示
4. 使用 Google Authenticator 扫描二维码
5. 输入 6 位验证码完成设置

**期望结果**：
- 密钥生成成功，显示二维码和文本密钥
- 可以复制密钥到剪贴板
- 验证码验证成功后显示设置完成页面

### 测试用例 5: 错误处理验证

**目标**：验证各种错误情况的处理

**测试场景**：
1. **网络错误**：断开网络连接后尝试登录
2. **reCAPTCHA 过期**：等待 reCAPTCHA 过期后提交
3. **无效验证码**：输入错误的 2FA 验证码
4. **配置错误**：使用错误的 reCAPTCHA 密钥

**期望结果**：
- 每种错误都有相应的用户友好提示
- 错误后相关组件会重置状态
- 不会出现未处理的异常

## API 端点测试

### 1. 获取 reCAPTCHA 配置
```bash
curl -X GET http://localhost:3000/api/admin/auth/recaptcha-config
```

### 2. 登录（带 reCAPTCHA）
```bash
curl -X POST http://localhost:3000/api/admin/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "password123",
    "captcha": "ABCD",
    "captcha_key": "captcha-key-uuid",
    "recaptcha_token": "recaptcha-response-token"
  }'
```

### 3. 生成 2FA 密钥
```bash
curl -X POST http://localhost:3000/api/admin/auth/2fa/generate \
  -H "Authorization: Bearer your-jwt-token"
```

### 4. 验证 2FA 设置
```bash
curl -X POST http://localhost:3000/api/admin/auth/2fa/verify \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{"code": "123456"}'
```

## 前端测试

### 1. 组件加载测试
- 验证 reCAPTCHA 组件是否正确加载
- 验证配置禁用时 reCAPTCHA 不显示
- 验证加载状态和错误状态的显示

### 2. 表单验证测试
- 验证必填字段的验证规则
- 验证 reCAPTCHA 完成状态的检查
- 验证表单提交前的完整性检查

### 3. 路由跳转测试
- 验证 2FA 强制绑定的路由跳转
- 验证设置完成后的正确跳转
- 验证权限控制和路由守卫

## 性能测试

### 1. 并发登录测试
- 模拟多用户同时登录
- 验证 reCAPTCHA 验证的性能
- 检查 Redis 缓存的效率

### 2. 2FA 设置性能
- 测试密钥生成的响应时间
- 验证二维码生成的性能
- 检查 TOTP 验证的准确性

## 安全测试

### 1. 重放攻击防护
- 验证 reCAPTCHA token 不能重复使用
- 验证图形验证码的一次性使用
- 检查 2FA 验证码的时间窗口

### 2. 暴力破解防护
- 测试连续失败登录的处理
- 验证验证码刷新机制
- 检查账户锁定策略

## 故障排除

### 常见问题

1. **reCAPTCHA 不显示**
   - 检查 RECAPTCHA_ENABLED 配置
   - 验证 RECAPTCHA_SITE_KEY 是否正确
   - 检查网络连接和防火墙设置

2. **2FA 二维码无法扫描**
   - 验证 TOTP 密钥生成是否正确
   - 检查二维码 URL 格式
   - 确认 Google Authenticator 应用版本

3. **验证码验证失败**
   - 检查服务器时间同步
   - 验证 TOTP 时间窗口设置
   - 确认密钥存储和检索正确

### 日志检查

开发环境下，后端会输出详细的调试日志：
```
[DEV] Captcha Generated - Key: xxx, Answer: xxx
[reCAPTCHA] 验证结果: Success=true, ErrorCodes=[]
User 'username' (ID=1) requires 2FA setup on first login
User 'username' (ID=1) enabled 2FA successfully
```

## 部署注意事项

1. **生产环境配置**
   - 设置正确的域名到 reCAPTCHA 控制台
   - 使用强密码保护 reCAPTCHA 密钥
   - 配置适当的 Redis 过期时间

2. **监控和告警**
   - 监控 reCAPTCHA 验证成功率
   - 跟踪 2FA 设置完成率
   - 设置异常登录告警

3. **备份和恢复**
   - 备份用户 2FA 密钥（加密存储）
   - 准备 2FA 重置流程
   - 文档化紧急恢复步骤
