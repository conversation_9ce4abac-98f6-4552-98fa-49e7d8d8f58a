---
description: 
globs: 
alwaysApply: false
---
# API 注释规范 (<PERSON>wagg<PERSON>)

本文档规定了使用 `github.com/swaggo/swag` 为Go Fiber API项目生成Swagger文档的注释规范。

## 基本结构

每个需要生成文档的API处理器函数上方都应添加块注释，包含以下Swaggo注解：

```go
// @Summary      简短描述 (单行)
// @Description  详细描述 (可多行)
// @Tags         API分组标签 (例如: AdminUsers, Auth)
// @Accept       可接受的请求内容类型 (例如: json, xml, multipart/form-data)
// @Produce      可生成的响应内容类型 (例如: json, xml)
// @Param        参数描述 (可多个)
// @Success      成功响应描述 (可多个，按状态码区分)
// @Failure      失败响应描述 (可多个，按状态码区分)
// @Router       路由路径和HTTP方法
func YourHandlerFunction(c *fiber.Ctx) error {
    // ... handler logic ...
}
```

## 注解详解

- **`@Summary`**: API的简短总结，显示在Swagger UI的列表视图中。
- **`@Description`**: API的详细说明，支持Markdown格式。
- **`@Tags`**: 用于在Swagger UI中对API进行分组。可以有多个Tag，每个Tag占一行。
- **`@Accept`**: 定义API可以接受的`Content-Type`。常用值：`json`, `xml`, `plain`, `html`, `mpfd` (multipart/form-data), `x-www-form-urlencoded`。
- **`@Produce`**: 定义API可以生成的`Content-Type`。常用值同`@Accept`。

- **`@Param`**: 定义API的参数。
  - 格式: `@Param <参数名> <参数位置> <数据类型> <是否必需> "<描述>" [其他属性]`
  - **`<参数名>`**: 参数的名称。
  - **`<参数位置>`**: `path`, `query`, `header`, `body`, `formData`。
  - **`<数据类型>`**: Go的基本类型 (`string`, `int`, `int64`, `bool`, `float32`) 或自定义结构体 (`dto.AdminUserCreateDTO`)。
  - **`<是否必需>`**: `true` 或 `false`。
  - **`<描述>`**: 参数的说明。
  - **`[其他属性]`**: 可选，例如 `Enums(1, 2)`, `Default(10)`, `Minimum(1)`, `Maximum(100)`, `Format(email)`。
  - 示例:
    ```go
    // @Param id path int true "用户ID" Minimum(1)
    // @Param page query int false "页码" Default(1)
    // @Param page_size query int false "每页数量" Default(10) Maximum(100)
    // @Param user body dto.AdminUserCreateDTO true "创建用户请求体"
    ```

- **`@Success`**: 定义成功的HTTP响应。
  - 格式: `@Success <状态码> {<响应类型>} [<响应模型>] "<描述>"`
  - **`<状态码>`**: HTTP状态码 (例如: 200, 201, 204)。
  - **`<响应类型>`**: `object` (对象), `array` (数组), `string`, `integer`。
  - **`<响应模型>`**: 返回的数据结构类型。可以是基本类型或自定义结构体。对于标准响应，使用 `dto.StandardResponse{data=实际数据类型}`。
  - **`<描述>`**: 响应的说明。
  - 示例:
    ```go
    // @Success 200 {object} dto.StandardResponse{data=dto.AdminUserResponse} "获取成功"
    // @Success 201 {object} dto.StandardResponse{data=object{id=int}} "创建成功"
    // @Success 204 "删除成功 (无内容)"
    // @Success 200 {object} dto.StandardResponse{data=[]dto.AdminUserResponse} "获取列表成功"
    ```

- **`@Success`**: 分页响应示例
  - 对于分页数据，使用 `dto.StandardResponse{data=dto.PaginatedResponse{items=[]实际数据类型}}` 格式
  - 示例:
    ```go
    // @Success 200 {object} dto.StandardResponse{data=dto.PaginatedResponse{items=[]dto.AdminUserResponse}} "获取分页列表成功"
    ```

- **`@Failure`**: 定义失败的HTTP响应。
  - 格式: `@Failure <状态码> {object} <错误响应模型> "<描述>"`
  - 通常使用标准的 `dto.StandardResponse` 作为错误响应模型。
  - 示例:
    ```go
    // @Failure 400 {object} dto.StandardResponse "请求参数错误"
    // @Failure 401 {object} dto.StandardResponse "未授权"
    // @Failure 404 {object} dto.StandardResponse "资源未找到"
    // @Failure 500 {object} dto.StandardResponse "服务器内部错误"
    ```

- **`@Router`**: 定义API的路由路径和HTTP方法。
  - 格式: `@Router <路由路径> [<HTTP方法>]`
  - **`<路由路径>`**: 相对Base Path的路径，路径参数用 `{}` 包裹 (例如: `/users/{id}` )。
  - **`<HTTP方法>`**: `get`, `post`, `put`, `patch`, `delete`。
  - 示例:
    ```go
    // @Router /users [post]
    // @Router /users/{id} [get]
    ```

## 响应结构说明

### 标准响应结构 (dto.StandardResponse)

所有API都应使用标准响应结构包装实际数据，确保响应格式统一：

```json
{
  "code": 0,        // 业务状态码，0表示成功
  "message": "操作成功", // 响应消息
  "data": {},       // 实际响应数据
  "error": ""       // 错误信息，仅在开发环境显示
}
```

### 分页响应结构 (dto.PaginatedResponse)

列表数据应使用分页响应结构：

```json
{
  "code": 0,
  "message": "获取成功",
  "data": {
    "items": [],     // 当前页数据项
    "total": 100,    // 总记录数
    "page": 1,       // 当前页码
    "page_size": 10, // 每页大小
    "total_pages": 10 // 总页数
  }
}
```

## 生成文档

在添加或修改注释后，在项目根目录运行以下命令更新Swagger文档：

```bash
swag init
```

确保 `main.go` 中导入了生成的 `docs` 包 (`_ "your_module_path/docs"`)。

## 注意事项

- 注释必须紧邻函数声明。
- 确保注解的格式正确，特别是空格和参数顺序。
- 使用 `dto` 包中定义的结构体来描述请求体和响应数据模型。
- 保持注释简洁明了。
- 分页接口必须包含page和page_size参数，并使用分页响应结构。
