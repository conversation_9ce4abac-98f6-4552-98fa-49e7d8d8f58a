package routes

import (
	"go-fiber-api/api/handlers"
	"go-fiber-api/api/middleware"
	"go-fiber-api/utils"

	"github.com/gofiber/fiber/v2"
)

// SetupInvitationRoutes 设置邀请码管理相关路由
func SetupInvitationRoutes(router fiber.Router) {
	// 邀请码路由组
	invitations := router.Group("/invitation")
	
	// 添加认证中间件
	invitations.Use(utils.RequireAuthentication)

	// 邀请码CRUD路由
	invitations.Get("/index", middleware.RequirePermission("invitation.view"), handlers.GetInvitations)           // 获取邀请码列表
	invitations.Get("/index/:id", middleware.RequirePermission("invitation.view"), handlers.GetInvitation)        // 获取单个邀请码
	invitations.Post("/index", middleware.RequirePermission("invitation.create"), handlers.CreateInvitation)        // 创建邀请码
	invitations.Put("/index/:id", middleware.RequirePermission("invitation.edit"), handlers.UpdateInvitation)     // 更新邀请码
	invitations.Delete("/index/:id", middleware.RequirePermission("invitation.delete"), handlers.DeleteInvitation)  // 删除邀请码
	
	// 批量操作路由
	invitations.Post("/batch", middleware.RequirePermission("invitation.batch"), handlers.BatchOperateInvitations) // 批量操作邀请码
} 