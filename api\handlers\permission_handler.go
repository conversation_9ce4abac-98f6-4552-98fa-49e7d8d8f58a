package handlers

import (
	"go-fiber-api/api/dto"
	"go-fiber-api/database"
	"go-fiber-api/models"
	"go-fiber-api/utils"
	"strconv"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

// GetPermissions 获取权限列表
// @Summary 获取权限列表
// @Description 获取系统中的所有权限
// @Tags 权限管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param name query string false "权限名称(模糊搜索)"
// @Param parent_id query int false "父权限ID"
// @Success 200 {object} dto.StandardResponse{data=[]dto.PermissionResponse}
// @Router /admin/permissions [get]
func GetPermissions(c *fiber.Ctx) error {
	// 获取查询参数
	name := c.Query("name", "")
	parentIDStr := c.Query("parent_id", "")
	var parentID uint64 = 0
	if parentIDStr != "" {
		var err error
		parentID, err = strconv.ParseUint(parentIDStr, 10, 64)
		if err != nil {
			return utils.BadRequest(c, "无效的父权限ID", err)
		}
	}

	// 构建查询
	query := database.DB.Model(&models.Permission{})

	// 应用筛选条件
	if name != "" {
		query = query.Where("name LIKE ?", "%"+name+"%")
	}
	if parentIDStr != "" {
		query = query.Where("parent_id = ?", parentID)
	}

	// 获取权限列表
	var permissions []models.Permission
	if err := query.Order("parent_id ASC, `order` ASC").Find(&permissions).Error; err != nil {
		return utils.ServerError(c, "获取权限列表失败", err)
	}

	// 转换为响应DTO
	var response []dto.PermissionResponse
	for _, p := range permissions {
		response = append(response, dto.PermissionResponse{
			ID:         p.ID,
			Name:       p.Name,
			Slug:       p.Slug,
			HttpMethod: p.HttpMethod,
			HttpPath:   p.HttpPath,
			ParentID:   p.ParentID,
			Order:      p.Order,
		})
	}

	// 如果要求树状结构
	if c.Query("tree", "false") == "true" {
		return utils.Success(c, "获取权限树成功", buildPermissionTree(response, 0))
	}

	return utils.Success(c, "获取权限列表成功", response)
}

// GetPermissionTree 获取权限树
// @Summary 获取权限树
// @Description 获取系统中的所有权限，以树形结构返回
// @Tags 权限管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param exclude_ids query string false "排除的权限ID，多个ID用逗号分隔"
// @Success 200 {object} dto.StandardResponse{data=[]dto.PermissionResponse}
// @Router /admin/permissions/tree [get]
func GetPermissionTree(c *fiber.Ctx) error {
	// 获取要排除的ID列表
	excludeIDs := []uint64{}
	excludeIDsStr := c.Query("exclude_ids", "")
	if excludeIDsStr != "" {
		for _, idStr := range utils.StringToSlice(excludeIDsStr, ",") {
			id, err := strconv.ParseUint(idStr, 10, 64)
			if err == nil {
				excludeIDs = append(excludeIDs, id)
			}
		}
	}

	// 构建查询
	query := database.DB.Model(&models.Permission{})
	
	// 排除指定ID
	if len(excludeIDs) > 0 {
		query = query.Where("id NOT IN ?", excludeIDs)
	}

	// 获取权限列表
	var permissions []models.Permission
	if err := query.Order("parent_id ASC, `order` ASC").Find(&permissions).Error; err != nil {
		return utils.ServerError(c, "获取权限列表失败", err)
	}

	// 转换为响应DTO
	var response []dto.PermissionResponse
	for _, p := range permissions {
		response = append(response, dto.PermissionResponse{
			ID:         p.ID,
			Name:       p.Name,
			Slug:       p.Slug,
			HttpMethod: p.HttpMethod,
			HttpPath:   p.HttpPath,
			ParentID:   p.ParentID,
			Order:      p.Order,
		})
	}

	// 构建树形结构
	return utils.Success(c, "获取权限树成功", buildPermissionTree(response, 0))
}

// GetPermissionDetail 获取权限详情
// @Summary 获取权限详情
// @Description 获取指定ID的权限详情
// @Tags 权限管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "权限ID"
// @Success 200 {object} dto.StandardResponse{data=dto.PermissionDetailResponse}
// @Failure 404 {object} dto.StandardResponse "权限不存在"
// @Router /admin/permissions/{id} [get]
func GetPermissionDetail(c *fiber.Ctx) error {
	// 获取权限ID
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的权限ID", err)
	}

	// 查询权限
	var permission models.Permission
	if err := database.DB.Preload("Roles").Preload("Menus").First(&permission, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "权限不存在")
		}
		return utils.ServerError(c, "获取权限失败", err)
	}

	// 获取关联的角色ID列表
	var roleIDs []uint64
	for _, role := range permission.Roles {
		roleIDs = append(roleIDs, role.ID)
	}

	// 获取关联的菜单ID列表
	var menuIDs []uint64
	for _, menu := range permission.Menus {
		menuIDs = append(menuIDs, menu.ID)
	}

	// 转换为DTO
	response := dto.PermissionDetailResponse{
		ID:         permission.ID,
		Name:       permission.Name,
		Slug:       permission.Slug,
		HttpMethod: permission.HttpMethod,
		HttpPath:   permission.HttpPath,
		Order:      permission.Order,
		ParentID:   permission.ParentID,
		RoleIDs:    roleIDs,
		MenuIDs:    menuIDs,
	}

	return utils.Success(c, "获取权限详情成功", response)
}

// CreatePermission 创建权限
// @Summary 创建权限
// @Description 创建一个新的权限
// @Tags 权限管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param permission body dto.PermissionCreateDTO true "权限信息"
// @Success 200 {object} dto.StandardResponse{data=dto.PermissionResponse}
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Router /admin/permissions [post]
func CreatePermission(c *fiber.Ctx) error {
	// 解析请求体
	var req dto.PermissionCreateDTO
	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}

	// 验证请求数据
	if err := utils.ValidateStruct(req); err != nil {
		return utils.BadRequest(c, "数据验证失败", err)
	}

	// 检查slug唯一性
	var count int64
	database.DB.Model(&models.Permission{}).Where("slug = ?", req.Slug).Count(&count)
	if count > 0 {
		return utils.BadRequest(c, "权限标识已存在", nil)
	}

	// 创建权限
	permission := models.Permission{
		Name:       req.Name,
		Slug:       req.Slug,
		HttpMethod: req.HttpMethod,
		HttpPath:   req.HttpPath,
		Order:      req.Order,
		ParentID:   req.ParentID,
	}

	err := database.DB.Transaction(func(tx *gorm.DB) error {
		// 创建权限
		if err := tx.Create(&permission).Error; err != nil {
			return err
		}

		// 处理角色关联
		if len(req.RoleIDs) > 0 {
			var roles []models.Role
			if err := tx.Where("id IN ?", req.RoleIDs).Find(&roles).Error; err != nil {
				return err
			}
			if err := tx.Model(&permission).Association("Roles").Replace(roles); err != nil {
				return err
			}
		}

		// 处理菜单关联
		if len(req.MenuIDs) > 0 {
			var menus []models.Menu
			if err := tx.Where("id IN ?", req.MenuIDs).Find(&menus).Error; err != nil {
				return err
			}
			if err := tx.Model(&permission).Association("Menus").Replace(menus); err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return utils.ServerError(c, "创建权限失败", err)
	}

	// 转换为DTO
	response := dto.PermissionResponse{
		ID:         permission.ID,
		Name:       permission.Name,
		Slug:       permission.Slug,
		HttpMethod: permission.HttpMethod,
		HttpPath:   permission.HttpPath,
		Order:      permission.Order,
		ParentID:   permission.ParentID,
	}

	return utils.Success(c, "创建权限成功", response)
}

// UpdatePermission 更新权限
// @Summary 更新权限
// @Description 更新已存在的权限信息
// @Tags 权限管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "权限ID"
// @Param permission body dto.PermissionUpdateDTO true "权限更新信息"
// @Success 200 {object} dto.StandardResponse{data=dto.PermissionResponse}
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 404 {object} dto.StandardResponse "权限不存在"
// @Router /admin/permissions/{id} [put]
func UpdatePermission(c *fiber.Ctx) error {
	// 获取权限ID
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的权限ID", err)
	}

	// 查询权限
	var permission models.Permission
	if err := database.DB.First(&permission, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "权限不存在")
		}
		return utils.ServerError(c, "获取权限失败", err)
	}

	// 解析请求体
	var req dto.PermissionUpdateDTO
	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}

	// 验证请求数据
	if err := utils.ValidateStruct(req); err != nil {
		return utils.BadRequest(c, "数据验证失败", err)
	}

	// 检查slug唯一性（排除自身）
	if req.Slug != permission.Slug {
		var count int64
		database.DB.Model(&models.Permission{}).Where("slug = ? AND id != ?", req.Slug, id).Count(&count)
		if count > 0 {
			return utils.BadRequest(c, "权限标识已存在", nil)
		}
	}

	// 更新权限
	permission.Name = req.Name
	permission.Slug = req.Slug
	permission.HttpMethod = req.HttpMethod
	permission.HttpPath = req.HttpPath
	permission.Order = req.Order
	permission.ParentID = req.ParentID

	err = database.DB.Transaction(func(tx *gorm.DB) error {
		// 更新权限基本信息
		if err := tx.Save(&permission).Error; err != nil {
			return err
		}

		// 处理角色关联
		if req.RoleIDs != nil {
			var roles []models.Role
			if err := tx.Where("id IN ?", req.RoleIDs).Find(&roles).Error; err != nil {
				return err
			}
			if err := tx.Model(&permission).Association("Roles").Replace(roles); err != nil {
				return err
			}
		}

		// 处理菜单关联
		if req.MenuIDs != nil {
			var menus []models.Menu
			if err := tx.Where("id IN ?", req.MenuIDs).Find(&menus).Error; err != nil {
				return err
			}
			if err := tx.Model(&permission).Association("Menus").Replace(menus); err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return utils.ServerError(c, "更新权限失败", err)
	}

	// 转换为DTO
	response := dto.PermissionResponse{
		ID:         permission.ID,
		Name:       permission.Name,
		Slug:       permission.Slug,
		HttpMethod: permission.HttpMethod,
		HttpPath:   permission.HttpPath,
		Order:      permission.Order,
		ParentID:   permission.ParentID,
	}

	return utils.Success(c, "更新权限成功", response)
}

// DeletePermission 删除权限
// @Summary 删除权限
// @Description 删除指定的权限
// @Tags 权限管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "权限ID"
// @Success 200 {object} dto.StandardResponse "删除成功"
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 404 {object} dto.StandardResponse "权限不存在"
// @Router /admin/permissions/{id} [delete]
func DeletePermission(c *fiber.Ctx) error {
	// 获取权限ID
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的权限ID", err)
	}

	// 检查是否有子权限
	var childCount int64
	if err := database.DB.Model(&models.Permission{}).Where("parent_id = ?", id).Count(&childCount).Error; err != nil {
		return utils.ServerError(c, "检查子权限失败", err)
	}

	if childCount > 0 {
		return utils.BadRequest(c, "无法删除含有子权限的权限", nil)
	}

	// 查询权限
	var permission models.Permission
	if err := database.DB.First(&permission, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "权限不存在")
		}
		return utils.ServerError(c, "获取权限失败", err)
	}

	err = database.DB.Transaction(func(tx *gorm.DB) error {
		// 清除关联
		if err := tx.Model(&permission).Association("Roles").Clear(); err != nil {
			return err
		}
		if err := tx.Model(&permission).Association("Menus").Clear(); err != nil {
			return err
		}

		// 删除权限
		if err := tx.Delete(&permission).Error; err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return utils.ServerError(c, "删除权限失败", err)
	}

	return utils.Success(c, "删除权限成功", nil)
}

// BatchDeletePermissions 批量删除权限
// @Summary 批量删除权限
// @Description 批量删除指定ID的权限
// @Tags 权限管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param ids body dto.BatchIDsDTO true "权限ID列表"
// @Success 200 {object} dto.StandardResponse "删除成功"
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Router /admin/permissions/batch-delete [post]
func BatchDeletePermissions(c *fiber.Ctx) error {
	// 解析请求体
	var req dto.BatchIDsDTO
	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}

	// 验证请求数据
	if err := utils.ValidateStruct(req); err != nil {
		return utils.BadRequest(c, "数据验证失败", err)
	}

	if len(req.IDs) == 0 {
		return utils.BadRequest(c, "未指定要删除的权限", nil)
	}

	// 检查是否有子权限
	var parentIDs []uint64
	if err := database.DB.Model(&models.Permission{}).Where("id IN ?", req.IDs).Pluck("id", &parentIDs).Error; err != nil {
		return utils.ServerError(c, "获取权限ID失败", err)
	}

	var childCount int64
	if err := database.DB.Model(&models.Permission{}).Where("parent_id IN ?", parentIDs).Count(&childCount).Error; err != nil {
		return utils.ServerError(c, "检查子权限失败", err)
	}

	if childCount > 0 {
		return utils.BadRequest(c, "无法删除含有子权限的权限", nil)
	}

	err := database.DB.Transaction(func(tx *gorm.DB) error {
		// 清除关联关系
		var permissions []models.Permission
		if err := tx.Where("id IN ?", req.IDs).Find(&permissions).Error; err != nil {
			return err
		}

		for _, perm := range permissions {
			if err := tx.Model(&perm).Association("Roles").Clear(); err != nil {
				return err
			}
			if err := tx.Model(&perm).Association("Menus").Clear(); err != nil {
				return err
			}
		}

		// 删除权限
		if err := tx.Where("id IN ?", req.IDs).Delete(&models.Permission{}).Error; err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return utils.ServerError(c, "批量删除权限失败", err)
	}

	return utils.Success(c, "批量删除权限成功", nil)
}

// CheckUserPermission 检查用户是否有特定权限
// @Summary 检查用户权限
// @Description 检查当前用户是否有特定的权限标识
// @Tags 权限管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param slug path string true "权限标识"
// @Success 200 {object} dto.StandardResponse{data=bool}
// @Router /admin/permissions/check/{slug} [get]
func CheckUserPermission(c *fiber.Ctx) error {
	// 获取权限标识
	slug := c.Params("slug")
	if slug == "" {
		return utils.BadRequest(c, "未提供权限标识", nil)
	}

	// 获取当前用户
	user, err := utils.GetCurrentUserFromContext(c)
	if err != nil {
		return utils.ServerError(c, "获取用户信息失败", err)
	}

	// 检查是否为超级管理员
	for _, role := range user.Roles {
		if role.Name == "administrator" {
			return utils.Success(c, "用户有权限访问", true)
		}
	}

	// 检查用户是否有特定权限
	hasPermission := false
	
	// 获取用户所有角色ID
	var roleIDs []uint64
	for _, role := range user.Roles {
		roleIDs = append(roleIDs, role.ID)
	}

	// 如果用户有角色
	if len(roleIDs) > 0 {
		// 查询用户角色是否具有该权限
		var count int64
		query := `
			SELECT COUNT(*) 
			FROM admin_permissions p 
			JOIN admin_role_permissions rp ON p.id = rp.permission_id 
			WHERE p.slug = ? AND rp.role_id IN ?
		`
		database.DB.Raw(query, slug, roleIDs).Count(&count)
		
		if count > 0 {
			hasPermission = true
		}
	}

	if hasPermission {
		return utils.Success(c, "用户有权限访问", true)
	} else {
		return utils.Success(c, "用户无权限访问", false)
	}
}

// buildPermissionTree 递归构建权限树
func buildPermissionTree(permissions []dto.PermissionResponse, parentID uint64) []dto.PermissionResponse {
	var tree []dto.PermissionResponse

	for _, perm := range permissions {
		if perm.ParentID == parentID {
			node := perm
			
			// 查找子权限
			children := buildPermissionTree(permissions, perm.ID)
			if len(children) > 0 {
				node.Children = children
			}
			
			tree = append(tree, node)
		}
	}

	return tree
} 