package models

import (
	"time"
)

// FailedJob 失败任务模型
type FailedJob struct {
	ID         uint64    `json:"id" gorm:"primaryKey;type:bigint unsigned"`
	Connection string    `json:"connection" gorm:"type:text;not null"`
	Queue      string    `json:"queue" gorm:"type:text;not null"`
	Payload    string    `json:"payload" gorm:"type:longtext;not null"`
	Exception  string    `json:"exception" gorm:"type:longtext;not null"`
	FailedAt   time.Time `json:"failed_at" gorm:"type:timestamp;not null;autoCreateTime"`
}

// TableName 指定表名
func (FailedJob) TableName() string {
	return "failed_jobs"
} 
