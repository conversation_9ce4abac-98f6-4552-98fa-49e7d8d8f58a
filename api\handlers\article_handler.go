package handlers

import (
	"fmt"
	"go-fiber-api/api/dto"
	"go-fiber-api/database"
	"go-fiber-api/models"
	"go-fiber-api/utils"
	"go-fiber-api/utils/queue"
	"strconv"
	"time"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

// GetArticles 获取公告列表
// @Summary 获取公告列表
// @Description 获取所有公告列表，支持分页
// @Tags 公告管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码，默认为1"
// @Param page_size query int false "每页条数，默认为10"
// @Param status query int false "状态过滤，1-启用，0-禁用"
// @Success 200 {object} dto.StandardResponse{data=dto.PaginatedResponse{items=[]dto.ArticleListItem}}
// @Router /admin/article/index [get]
func GetArticles(c *fiber.Ctx) error {
	db := database.DB.Model(&models.Article{})

	// 获取分页参数
	page, _ := strconv.Atoi(c.Query("page", "1"))
	pageSize, _ := strconv.Atoi(c.Query("page_size", "10"))
	status := c.Query("status", "")

	// 构建查询
	query := db.Model(&models.Article{})

	// 应用过滤
	if status != "" {
		statusInt, _ := strconv.Atoi(status)
		query = query.Where("status = ?", statusInt)
	}

	// 计算总数
	var total int64
	query.Count(&total)

	// 获取分页数据
	var articles []models.Article
	query.Order("id DESC").
		Offset((page - 1) * pageSize).
		Limit(pageSize).
		Find(&articles)

	// 转换为DTO
	var articleDTOs []dto.ArticleListItem
	for _, article := range articles {
		articleDTOs = append(articleDTOs, dto.ArticleListItem{
			ID:        article.ID,
			Title:     article.Title,
			Status:    article.Status,
			Author:    article.Author,
			CreatedAt: article.CreatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	totalPages := (int(total) + pageSize - 1) / pageSize

	// 返回分页响应
	return utils.Success(c, "获取公告列表成功", dto.PaginatedResponse{
		Items:      articleDTOs,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	})
}

// GetArticle 获取公告详情
// @Summary 获取公告详情
// @Description 根据ID获取公告详情
// @Tags 公告管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "公告ID"
// @Success 200 {object} dto.StandardResponse{data=dto.ArticleResponse}
// @Router /admin/article/index/{id} [get]
func GetArticle(c *fiber.Ctx) error {
	db := database.DB.Model(&models.Article{})
	id := c.Params("id")

	var article models.Article
	if err := db.First(&article, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "公告不存在")
		}
		return utils.ServerError(c, "获取公告失败", err)
	}

	// 转换为DTO
	articleDTO := dto.ArticleResponse{
		ID:          article.ID,
		Title:       article.Title,
		Description: article.Description,
		Status:      article.Status,
		Author:      article.Author,
		CreatedBy:   article.CreatedBy,
		UpdatedBy:   article.UpdatedBy,
		CreatedAt:   article.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:   article.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	return utils.Success(c, "获取公告成功", articleDTO)
}

// CreateArticle 创建公告
// @Summary 创建公告
// @Description 创建新的公告
// @Tags 公告管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param article body dto.ArticleRequest true "公告信息"
// @Success 200 {object} dto.StandardResponse{data=dto.ArticleResponse}
// @Router /admin/article/index [post]
func CreateArticle(c *fiber.Ctx) error {
	db := database.DB.Model(&models.Article{})

	// 解析请求体
	var req dto.ArticleRequest
	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}

	// 验证请求数据
	if err := utils.ValidateStruct(req); err != nil {
		return utils.BadRequest(c, "数据验证失败", err)
	}

	// 获取当前用户ID
	userID := utils.GetUserIDFromContext(c)

	// 创建新公告
	article := models.Article{
		Title:       req.Title,
		Description: req.Description,
		Status:      *req.Status,
		Author:      req.Author,
		CreatedBy:   userID,
		UpdatedBy:   userID,
	}

	if err := db.Create(&article).Error; err != nil {
		return utils.ServerError(c, "创建公告失败", err)
	}

	// 如果status为1，表示需要发布公告
	if *req.Status == 1 {
		// 添加到队列进行发布
		if err := queue.EnqueueArticlePublishTask(
			article.ID,
			article.Title,
			article.Description,
			article.Author,
			0, // 不延迟，立即发布
		); err != nil {
			// 发布失败但创建成功，返回警告信息
			return utils.Success(c, "公告创建成功，但发布失败，请稍后手动发布", dto.ArticleResponse{
				ID:          article.ID,
				Title:       article.Title,
				Description: article.Description,
				Status:      article.Status,
				Author:      article.Author,
				CreatedBy:   article.CreatedBy,
				UpdatedBy:   article.UpdatedBy,
				CreatedAt:   article.CreatedAt.Format("2006-01-02 15:04:05"),
				UpdatedAt:   article.UpdatedAt.Format("2006-01-02 15:04:05"),
			})
		}
	}

	// 转换为DTO
	articleDTO := dto.ArticleResponse{
		ID:          article.ID,
		Title:       article.Title,
		Description: article.Description,
		Status:      article.Status,
		Author:      article.Author,
		CreatedBy:   article.CreatedBy,
		UpdatedBy:   article.UpdatedBy,
		CreatedAt:   article.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:   article.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	return utils.Success(c, "创建公告成功", articleDTO)
}

// UpdateArticle 更新公告
// @Summary 更新公告
// @Description 更新现有公告信息
// @Tags 公告管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "公告ID"
// @Param article body dto.ArticleUpdateRequest true "公告更新信息"
// @Success 200 {object} dto.StandardResponse{data=dto.ArticleResponse}
// @Router /admin/article/index/{id} [put]
func UpdateArticle(c *fiber.Ctx) error {
	db := database.DB.Model(&models.Article{})
	id := c.Params("id")

	// 解析请求体
	var req dto.ArticleUpdateRequest
	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}

	// 验证请求数据
	if err := utils.ValidateStruct(req); err != nil {
		return utils.BadRequest(c, "数据验证失败", err)
	}

	// 获取当前用户ID
	userID := utils.GetUserIDFromContext(c)

	// 查找公告
	var article models.Article
	if err := db.First(&article, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "公告不存在")
		}
		return utils.ServerError(c, "获取公告失败", err)
	}

	// 更新字段
	updates := make(map[string]interface{})
	if req.Title != nil {
		updates["title"] = *req.Title
	}
	if req.Description != nil {
		updates["description"] = *req.Description
	}
	if req.Status != nil {
		updates["status"] = *req.Status
	}
	if req.Author != nil {
		updates["author"] = *req.Author
	}

	// 添加更新者ID和更新时间
	updates["updated_by"] = userID
	updates["updated_at"] = time.Now()

	// 执行更新
	if err := db.Model(&article).Updates(updates).Error; err != nil {
		return utils.ServerError(c, "更新公告失败", err)
	}

	// 重新获取更新后的公告
	if err := db.First(&article, id).Error; err != nil {
		return utils.ServerError(c, "获取更新后的公告失败", err)
	}

	// 转换为DTO
	articleDTO := dto.ArticleResponse{
		ID:          article.ID,
		Title:       article.Title,
		Description: article.Description,
		Status:      article.Status,
		Author:      article.Author,
		CreatedBy:   article.CreatedBy,
		UpdatedBy:   article.UpdatedBy,
		CreatedAt:   article.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:   article.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	return utils.Success(c, "更新公告成功", articleDTO)
}

// DeleteArticle 删除公告
// @Summary 删除公告
// @Description 根据ID删除公告
// @Tags 公告管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "公告ID"
// @Success 200 {object} dto.StandardResponse
// @Router /admin/article/index/{id} [delete]
func DeleteArticle(c *fiber.Ctx) error {
	db := database.DB.Model(&models.Article{})
	id := c.Params("id")

	// 查找公告
	var article models.Article
	if err := db.First(&article, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "公告不存在")
		}
		return utils.ServerError(c, "获取公告失败", err)
	}

	// 执行软删除
	if err := db.Delete(&article).Error; err != nil {
		return utils.ServerError(c, "删除公告失败", err)
	}

	return utils.Success(c, "删除公告成功", nil)
}

// PublishArticle 发布公告
// @Summary 发布公告
// @Description 将公告状态设置为已发布并通过队列发送通知
// @Tags 公告管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "公告ID"
// @Param delay_minutes query int false "延迟发布时间(分钟)"
// @Success 200 {object} dto.StandardResponse{data=dto.ArticleResponse}
// @Router /admin/article/publish/{id} [post]
func PublishArticle(c *fiber.Ctx) error {
	db := database.DB.Model(&models.Article{})
	id := c.Params("id")

	// 解析延迟参数
	delayMinutes, _ := strconv.Atoi(c.Query("delay_minutes", "0"))

	// 查找公告
	var article models.Article
	if err := db.First(&article, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "公告不存在")
		}
		return utils.ServerError(c, "获取公告失败", err)
	}

	// 检查公告状态
	if article.Status == 1 {
		return utils.BadRequest(c, "公告已经是发布状态", nil)
	}

	// 获取当前用户ID
	userID := utils.GetUserIDFromContext(c)

	// 更新公告字段
	updates := map[string]interface{}{
		"updated_by": userID,
		"updated_at": time.Now(),
	}

	if err := db.Model(&article).Updates(updates).Error; err != nil {
		return utils.ServerError(c, "更新公告失败", err)
	}

	// 添加到队列进行发布
	delay := time.Duration(delayMinutes) * time.Minute
	if err := queue.EnqueueArticlePublishTask(
		article.ID,
		article.Title,
		article.Description,
		article.Author,
		delay,
	); err != nil {
		return utils.ServerError(c, "添加发布任务失败", err)
	}

	// 如果没有延迟，直接更新状态为已发布
	if delayMinutes == 0 {
		if err := db.Model(&article).Update("status", 1).Error; err != nil {
			return utils.ServerError(c, "更新公告状态失败", err)
		}
	}

	// 重新获取更新后的公告
	if err := db.First(&article, id).Error; err != nil {
		return utils.ServerError(c, "获取更新后的公告失败", err)
	}

	// 转换为DTO
	articleDTO := dto.ArticleResponse{
		ID:          article.ID,
		Title:       article.Title,
		Description: article.Description,
		Status:      article.Status,
		Author:      article.Author,
		CreatedBy:   article.CreatedBy,
		UpdatedBy:   article.UpdatedBy,
		CreatedAt:   article.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:   article.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	// 根据是否延迟返回不同的成功消息
	message := "公告发布成功"
	if delayMinutes > 0 {
		message = fmt.Sprintf("公告将在%d分钟后发布", delayMinutes)
	}

	return utils.Success(c, message, articleDTO)
}
