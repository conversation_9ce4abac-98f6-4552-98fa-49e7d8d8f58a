package utils

import (
	"fmt"
	"math/rand"
	"time"
)

// GenerateRandomString 生成指定长度的随机字符串
// charSet: 指定字符集
// length: 指定生成长度
func GenerateRandomString(charSet string, length int) string {
	// 在方法内部创建随机源，避免与captcha.go的seededRand冲突
	localRand := rand.New(rand.NewSource(time.Now().UnixNano()))
	chars := []rune(charSet)
	result := make([]rune, length)
	for i := range result {
		result[i] = chars[localRand.Intn(len(chars))]
	}
	return string(result)
}

// GenerateRandomCode 生成随机邀请码
// length: 指定生成长度，默认为8
func GenerateRandomCode(length int) string {
	if length <= 0 {
		length = 8 // 默认长度为8
	}
	const charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	return GenerateRandomString(charset, length)
}

// GenerateRandomPassword 生成随机密码
// length: 指定生成长度，默认为12
func GenerateRandomPassword(length int) string {
	if length <= 0 {
		length = 12 // 默认长度为12
	}
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()-_=+"
	return GenerateRandomString(charset, length)
}

// GenerateRandomNumberString 生成随机数字字符串
// length: 指定生成长度
func GenerateRandomNumberString(length int) string {
	const charset = "0123456789"
	return GenerateRandomString(charset, length)
}

// GenerateUniqueID 生成唯一ID
// 基于时间戳和随机字符串，格式为: 前缀 + 时间戳 + 随机字符串
// 适用于生成任务编号等唯一标识
func GenerateUniqueID() string {
	timestamp := time.Now().UnixNano() / 1000000 // 毫秒级时间戳
	randomStr := GenerateRandomString("ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789", 6)
	return fmt.Sprintf("TASK%d%s", timestamp, randomStr)
} 