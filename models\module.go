package models

import (
	"time"

	"gorm.io/gorm"
)

// Module 模块模型
type Module struct {
	ID          uint64         `json:"id" gorm:"primaryKey;type:int unsigned;not null;comment:模块ID"`
	Title       string         `json:"title" gorm:"size:255;comment:模块名称"`
	Description string         `json:"description" gorm:"type:text;comment:模块说明(一句话说明)"`
	Keys        string         `json:"keys" gorm:"type:text;comment:关键字"`
	Setkeys     string         `json:"setkeys" gorm:"type:text;comment:配置参数"`
	Code        string         `json:"code" gorm:"type:mediumtext;comment:HTML代码"`
	Level       int8           `json:"level" gorm:"type:tinyint(1);default:0;comment:安全等级(0-9)"`
	UserID      uint64         `json:"user_id" gorm:"default:0;comment:创建人ID"`
	IsShare     int8           `json:"is_share" gorm:"type:tinyint;default:0;comment:共享状态(0 非公开/1 公开)"`
	State       int8           `json:"state" gorm:"type:tinyint(1);default:0;comment:模块状态"`
	CreatedAt   time.Time      `json:"created_at" gorm:"comment:创建时间"`
	UpdatedAt   time.Time      `json:"updated_at" gorm:"comment:更新时间"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index;comment:删除时间"`
}

// TableName 指定表名
func (Module) TableName() string {
	return "modules"
} 