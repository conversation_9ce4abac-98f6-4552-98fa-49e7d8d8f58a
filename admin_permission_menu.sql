/*
 Navicat Premium Data Transfer

 Source Server         : 1
 Source Server Type    : MySQL
 Source Server Version : 80034
 Source Host           : localhost:3306
 Source Schema         : go_fiber

 Target Server Type    : MySQL
 Target Server Version : 80034
 File Encoding         : 65001

 Date: 25/06/2025 01:40:16
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for admin_permission_menu
-- ----------------------------
DROP TABLE IF EXISTS `admin_permission_menu`;
CREATE TABLE `admin_permission_menu`  (
  `permission_id` bigint(0) NOT NULL,
  `menu_id` bigint(0) NOT NULL,
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  UNIQUE INDEX `admin_permission_menu_permission_id_menu_id_unique`(`permission_id`, `menu_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;
-- 系统权限控制台
INSERT INTO `admin_permission_menu` VALUES (31, 1, NOW(), NOW());

-- 项目管理相关权限
INSERT INTO `admin_permission_menu` VALUES (32, 2, NOW(), NOW()); -- 项目查看 -> 项目管理
INSERT INTO `admin_permission_menu` VALUES (32, 11, NOW(), NOW()); -- 项目查看 -> 项目列表
INSERT INTO `admin_permission_menu` VALUES (33, 12, NOW(), NOW()); -- 项目创建 -> 创建项目
INSERT INTO `admin_permission_menu` VALUES (34, 11, NOW(), NOW()); -- 项目编辑 -> 项目列表
INSERT INTO `admin_permission_menu` VALUES (35, 11, NOW(), NOW()); -- 项目删除 -> 项目列表
INSERT INTO `admin_permission_menu` VALUES (32, 13, NOW(), NOW()); -- 项目查看 -> 项目内容列表
-- 项目内容管理相关权限
INSERT INTO `admin_permission_menu` VALUES (97, 13, NOW(), NOW()); -- 项目内容查看 -> 项目内容列表
INSERT INTO `admin_permission_menu` VALUES (98, 13, NOW(), NOW()); -- 项目内容创建 -> 项目内容列表
INSERT INTO `admin_permission_menu` VALUES (99, 13, NOW(), NOW()); -- 项目内容编辑 -> 项目内容列表
INSERT INTO `admin_permission_menu` VALUES (100, 13, NOW(), NOW()); -- 项目内容删除 -> 项目内容列表
-- 模块管理相关权限
INSERT INTO `admin_permission_menu` VALUES (92, 3, NOW(), NOW()); -- 模块管理 -> 模块管理
INSERT INTO `admin_permission_menu` VALUES (36, 3, NOW(), NOW()); -- 模块查看 -> 模块管理
INSERT INTO `admin_permission_menu` VALUES (36, 14, NOW(), NOW()); -- 模块查看 -> 模块列表
INSERT INTO `admin_permission_menu` VALUES (37, 15, NOW(), NOW()); -- 模块创建 -> 创建模块
INSERT INTO `admin_permission_menu` VALUES (38, 14, NOW(), NOW()); -- 模块编辑 -> 模块列表
INSERT INTO `admin_permission_menu` VALUES (39, 14, NOW(), NOW()); -- 模块删除 -> 模块列表
INSERT INTO `admin_permission_menu` VALUES (93, 14, NOW(), NOW()); -- 模块内容查看 -> 模块列表
INSERT INTO `admin_permission_menu` VALUES (94, 14, NOW(), NOW()); -- 模块内容创建 -> 模块列表
INSERT INTO `admin_permission_menu` VALUES (95, 14, NOW(), NOW()); -- 模块内容编辑 -> 模块列表
INSERT INTO `admin_permission_menu` VALUES (96, 14, NOW(), NOW()); -- 模块内容删除 -> 模块列表

-- 域名管理相关权限
INSERT INTO `admin_permission_menu` VALUES (40, 4, NOW(), NOW()); -- 域名查看 -> 域名管理
INSERT INTO `admin_permission_menu` VALUES (40, 16, NOW(), NOW()); -- 域名查看 -> 域名列表
INSERT INTO `admin_permission_menu` VALUES (41, 17, NOW(), NOW()); -- 域名创建 -> 添加域名
INSERT INTO `admin_permission_menu` VALUES (42, 16, NOW(), NOW()); -- 域名编辑 -> 域名列表
INSERT INTO `admin_permission_menu` VALUES (43, 16, NOW(), NOW()); -- 域名删除 -> 域名列表

-- 邀请码管理相关权限
INSERT INTO `admin_permission_menu` VALUES (44, 5, NOW(), NOW()); -- 邀请码查看 -> 邀请码管理
INSERT INTO `admin_permission_menu` VALUES (44, 18, NOW(), NOW()); -- 邀请码查看 -> 邀请码列表
INSERT INTO `admin_permission_menu` VALUES (45, 19, NOW(), NOW()); -- 邀请码创建 -> 创建邀请码
INSERT INTO `admin_permission_menu` VALUES (46, 18, NOW(), NOW()); -- 邀请码编辑 -> 邀请码列表
INSERT INTO `admin_permission_menu` VALUES (47, 18, NOW(), NOW()); -- 邀请码删除 -> 邀请码列表

-- 友情链接相关权限
INSERT INTO `admin_permission_menu` VALUES (48, 6, NOW(), NOW()); -- 友情链接查看 -> 友情链接
INSERT INTO `admin_permission_menu` VALUES (48, 20, NOW(), NOW()); -- 友情链接查看 -> 链接列表
INSERT INTO `admin_permission_menu` VALUES (49, 21, NOW(), NOW()); -- 友情链接创建 -> 添加链接
INSERT INTO `admin_permission_menu` VALUES (50, 20, NOW(), NOW()); -- 友情链接编辑 -> 链接列表
INSERT INTO `admin_permission_menu` VALUES (51, 20, NOW(), NOW()); -- 友情链接删除 -> 链接列表

-- 菜单管理相关权限
INSERT INTO `admin_permission_menu` VALUES (52, 7, NOW(), NOW()); -- 菜单查看 -> 菜单管理
INSERT INTO `admin_permission_menu` VALUES (52, 22, NOW(), NOW()); -- 菜单查看 -> 菜单列表
INSERT INTO `admin_permission_menu` VALUES (53, 23, NOW(), NOW()); -- 菜单创建 -> 创建菜单
INSERT INTO `admin_permission_menu` VALUES (54, 22, NOW(), NOW()); -- 菜单编辑 -> 菜单列表
INSERT INTO `admin_permission_menu` VALUES (55, 22, NOW(), NOW()); -- 菜单删除 -> 菜单列表

-- 系统设置相关权限
INSERT INTO `admin_permission_menu` VALUES (56, 8, NOW(), NOW()); -- 设置查看 -> 系统设置
INSERT INTO `admin_permission_menu` VALUES (56, 24, NOW(), NOW()); -- 设置查看 -> 基本设置
INSERT INTO `admin_permission_menu` VALUES (57, 24, NOW(), NOW()); -- 设置修改 -> 基本设置

-- 用户管理相关权限
INSERT INTO `admin_permission_menu` VALUES (62, 25, NOW(), NOW()); -- 用户查看 -> 用户管理
INSERT INTO `admin_permission_menu` VALUES (63, 25, NOW(), NOW()); -- 用户创建 -> 用户管理
INSERT INTO `admin_permission_menu` VALUES (64, 25, NOW(), NOW()); -- 用户编辑 -> 用户管理
INSERT INTO `admin_permission_menu` VALUES (65, 25, NOW(), NOW()); -- 用户删除 -> 用户管理

-- 角色管理相关权限
INSERT INTO `admin_permission_menu` VALUES (66, 26, NOW(), NOW()); -- 角色查看 -> 角色管理
INSERT INTO `admin_permission_menu` VALUES (67, 26, NOW(), NOW()); -- 角色创建 -> 角色管理
INSERT INTO `admin_permission_menu` VALUES (68, 26, NOW(), NOW()); -- 角色编辑 -> 角色管理
INSERT INTO `admin_permission_menu` VALUES (69, 26, NOW(), NOW()); -- 角色删除 -> 角色管理

-- 权限管理相关权限
INSERT INTO `admin_permission_menu` VALUES (70, 50, NOW(), NOW()); -- 权限查看 -> 权限管理
INSERT INTO `admin_permission_menu` VALUES (71, 50, NOW(), NOW()); -- 权限创建 -> 权限管理
INSERT INTO `admin_permission_menu` VALUES (72, 50, NOW(), NOW()); -- 权限编辑 -> 权限管理
INSERT INTO `admin_permission_menu` VALUES (73, 50, NOW(), NOW()); -- 权限删除 -> 权限管理

-- 公告管理相关权限
INSERT INTO `admin_permission_menu` VALUES (58, 9, NOW(), NOW()); -- 公告查看 -> 公告管理
INSERT INTO `admin_permission_menu` VALUES (58, 27, NOW(), NOW()); -- 公告查看 -> 公告列表
INSERT INTO `admin_permission_menu` VALUES (59, 28, NOW(), NOW()); -- 公告创建 -> 发布公告
INSERT INTO `admin_permission_menu` VALUES (60, 27, NOW(), NOW()); -- 公告编辑 -> 公告列表
INSERT INTO `admin_permission_menu` VALUES (61, 27, NOW(), NOW()); -- 公告删除 -> 公告列表

SET FOREIGN_KEY_CHECKS = 1;
