package dto

// PermissionResponse 权限响应DTO
type PermissionResponse struct {
	ID         uint64                `json:"id"`
	Name       string                `json:"name"`
	Slug       string                `json:"slug"`
	HttpMethod string                `json:"http_method,omitempty"`
	HttpPath   string                `json:"http_path,omitempty"`
	Order      int                   `json:"order"`
	ParentID   uint64                `json:"parent_id"`
	Children   []PermissionResponse  `json:"children,omitempty"`
}

// PermissionDetailResponse 权限详情响应DTO
type PermissionDetailResponse struct {
	ID         uint64  `json:"id"`
	Name       string  `json:"name"`
	Slug       string  `json:"slug"`
	HttpMethod string  `json:"http_method,omitempty"`
	HttpPath   string  `json:"http_path,omitempty"`
	Order      int     `json:"order"`
	ParentID   uint64  `json:"parent_id"`
	RoleIDs    []uint64 `json:"role_ids,omitempty"`
	MenuIDs    []uint64 `json:"menu_ids,omitempty"`
}

// PermissionCreateDTO 创建权限请求DTO
type PermissionCreateDTO struct {
	Name       string  `json:"name" validate:"required,max=50"`
	Slug       string  `json:"slug" validate:"required,max=50"`
	HttpMethod string  `json:"http_method,omitempty"`
	HttpPath   string  `json:"http_path,omitempty"`
	Order      int     `json:"order" default:"0"`
	ParentID   uint64  `json:"parent_id" default:"0"`
	RoleIDs    []uint64 `json:"role_ids,omitempty"`
	MenuIDs    []uint64 `json:"menu_ids,omitempty"`
}

// PermissionUpdateDTO 更新权限请求DTO
type PermissionUpdateDTO struct {
	Name       string  `json:"name" validate:"required,max=50"`
	Slug       string  `json:"slug" validate:"required,max=50"`
	HttpMethod string  `json:"http_method,omitempty"`
	HttpPath   string  `json:"http_path,omitempty"`
	Order      int     `json:"order"`
	ParentID   uint64  `json:"parent_id"`
	RoleIDs    []uint64 `json:"role_ids,omitempty"`
	MenuIDs    []uint64 `json:"menu_ids,omitempty"`
}

// UserPermissionsResponse 用户权限响应DTO
type UserPermissionsResponse struct {
	Permissions []string `json:"permissions"`
	Roles       []string `json:"roles"`
}

// PermissionListItem 权限列表项
type PermissionListItem struct {
	ID         uint64               `json:"id"`
	Name       string               `json:"name"`
	Slug       string               `json:"slug"`
	HttpMethod string               `json:"http_method"`
	HttpPath   string               `json:"http_path"`
	Order      int                  `json:"order"`
	ParentID   uint64               `json:"parent_id"`
	Children   []PermissionListItem `json:"children,omitempty"`
}

// PermissionQuery 权限查询参数
type PermissionQuery struct {
	Name     string `query:"name"`
	Slug     string `query:"slug"`
	Page     int    `query:"page"`
	PageSize int    `query:"page_size"`
} 