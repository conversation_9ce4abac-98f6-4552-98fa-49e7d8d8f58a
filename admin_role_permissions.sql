/*
 Navicat Premium Data Transfer

 Source Server         : 1
 Source Server Type    : MySQL
 Source Server Version : 80034
 Source Host           : localhost:3306
 Source Schema         : go_fiber

 Target Server Type    : MySQL
 Target Server Version : 80034
 File Encoding         : 65001

 Date: 25/06/2025 01:40:35
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for admin_role_permissions
-- ----------------------------
DROP TABLE IF EXISTS `admin_role_permissions`;
CREATE TABLE `admin_role_permissions`  (
  `role_id` bigint(0) NOT NULL,
  `permission_id` bigint(0) NOT NULL,
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  UNIQUE INDEX `admin_role_permissions_role_id_permission_id_unique`(`role_id`, `permission_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;
-- 管理员拥有所有权限
INSERT INTO `admin_role_permissions` VALUES (1, 31, NOW(), NOW()); -- 管理员 - 系统权限控制台
INSERT INTO `admin_role_permissions` VALUES (1, 32, NOW(), NOW()); -- 管理员 - 项目查看
INSERT INTO `admin_role_permissions` VALUES (1, 33, NOW(), NOW()); -- 管理员 - 项目创建
INSERT INTO `admin_role_permissions` VALUES (1, 34, NOW(), NOW()); -- 管理员 - 项目编辑
INSERT INTO `admin_role_permissions` VALUES (1, 35, NOW(), NOW()); -- 管理员 - 项目删除
INSERT INTO `admin_role_permissions` VALUES (1, 36, NOW(), NOW()); -- 管理员 - 模块查看
INSERT INTO `admin_role_permissions` VALUES (1, 37, NOW(), NOW()); -- 管理员 - 模块创建
INSERT INTO `admin_role_permissions` VALUES (1, 38, NOW(), NOW()); -- 管理员 - 模块编辑
INSERT INTO `admin_role_permissions` VALUES (1, 39, NOW(), NOW()); -- 管理员 - 模块删除
INSERT INTO `admin_role_permissions` VALUES (1, 40, NOW(), NOW()); -- 管理员 - 域名查看
INSERT INTO `admin_role_permissions` VALUES (1, 41, NOW(), NOW()); -- 管理员 - 域名创建
INSERT INTO `admin_role_permissions` VALUES (1, 42, NOW(), NOW()); -- 管理员 - 域名编辑
INSERT INTO `admin_role_permissions` VALUES (1, 43, NOW(), NOW()); -- 管理员 - 域名删除
INSERT INTO `admin_role_permissions` VALUES (1, 44, NOW(), NOW()); -- 管理员 - 邀请码查看
INSERT INTO `admin_role_permissions` VALUES (1, 45, NOW(), NOW()); -- 管理员 - 邀请码创建
INSERT INTO `admin_role_permissions` VALUES (1, 46, NOW(), NOW()); -- 管理员 - 邀请码编辑
INSERT INTO `admin_role_permissions` VALUES (1, 47, NOW(), NOW()); -- 管理员 - 邀请码删除
INSERT INTO `admin_role_permissions` VALUES (1, 48, NOW(), NOW()); -- 管理员 - 友情链接查看
INSERT INTO `admin_role_permissions` VALUES (1, 49, NOW(), NOW()); -- 管理员 - 友情链接创建
INSERT INTO `admin_role_permissions` VALUES (1, 50, NOW(), NOW()); -- 管理员 - 友情链接编辑
INSERT INTO `admin_role_permissions` VALUES (1, 51, NOW(), NOW()); -- 管理员 - 友情链接删除
INSERT INTO `admin_role_permissions` VALUES (1, 52, NOW(), NOW()); -- 管理员 - 菜单查看
INSERT INTO `admin_role_permissions` VALUES (1, 53, NOW(), NOW()); -- 管理员 - 菜单创建
INSERT INTO `admin_role_permissions` VALUES (1, 54, NOW(), NOW()); -- 管理员 - 菜单编辑
INSERT INTO `admin_role_permissions` VALUES (1, 55, NOW(), NOW()); -- 管理员 - 菜单删除
INSERT INTO `admin_role_permissions` VALUES (1, 56, NOW(), NOW()); -- 管理员 - 设置查看
INSERT INTO `admin_role_permissions` VALUES (1, 57, NOW(), NOW()); -- 管理员 - 设置修改
INSERT INTO `admin_role_permissions` VALUES (1, 58, NOW(), NOW()); -- 管理员 - 公告查看
INSERT INTO `admin_role_permissions` VALUES (1, 59, NOW(), NOW()); -- 管理员 - 公告创建
INSERT INTO `admin_role_permissions` VALUES (1, 60, NOW(), NOW()); -- 管理员 - 公告编辑
INSERT INTO `admin_role_permissions` VALUES (1, 61, NOW(), NOW()); -- 管理员 - 公告删除
INSERT INTO `admin_role_permissions` VALUES (1, 62, NOW(), NOW()); -- 管理员 - 用户查看
INSERT INTO `admin_role_permissions` VALUES (1, 63, NOW(), NOW()); -- 管理员 - 用户创建
INSERT INTO `admin_role_permissions` VALUES (1, 64, NOW(), NOW()); -- 管理员 - 用户编辑
INSERT INTO `admin_role_permissions` VALUES (1, 65, NOW(), NOW()); -- 管理员 - 用户删除
INSERT INTO `admin_role_permissions` VALUES (1, 66, NOW(), NOW()); -- 管理员 - 角色查看
INSERT INTO `admin_role_permissions` VALUES (1, 67, NOW(), NOW()); -- 管理员 - 角色创建
INSERT INTO `admin_role_permissions` VALUES (1, 68, NOW(), NOW()); -- 管理员 - 角色编辑
INSERT INTO `admin_role_permissions` VALUES (1, 69, NOW(), NOW()); -- 管理员 - 角色删除
INSERT INTO `admin_role_permissions` VALUES (1, 70, NOW(), NOW()); -- 管理员 - 权限查看
INSERT INTO `admin_role_permissions` VALUES (1, 71, NOW(), NOW()); -- 管理员 - 权限创建
INSERT INTO `admin_role_permissions` VALUES (1, 72, NOW(), NOW()); -- 管理员 - 权限编辑
INSERT INTO `admin_role_permissions` VALUES (1, 73, NOW(), NOW()); -- 管理员 - 权限删除
INSERT INTO `admin_role_permissions` VALUES (1, 92, NOW(), NOW()); -- 管理员 - 模块管理
INSERT INTO `admin_role_permissions` VALUES (1, 93, NOW(), NOW()); -- 管理员 - 模块内容查看
INSERT INTO `admin_role_permissions` VALUES (1, 94, NOW(), NOW()); -- 管理员 - 模块内容创建
INSERT INTO `admin_role_permissions` VALUES (1, 95, NOW(), NOW()); -- 管理员 - 模块内容编辑
INSERT INTO `admin_role_permissions` VALUES (1, 96, NOW(), NOW()); -- 管理员 - 模块内容删除
INSERT INTO `admin_role_permissions` VALUES (1, 97, NOW(), NOW()); -- 管理员 - 项目内容查看
INSERT INTO `admin_role_permissions` VALUES (1, 98, NOW(), NOW()); -- 管理员 - 项目内容创建
INSERT INTO `admin_role_permissions` VALUES (1, 99, NOW(), NOW()); -- 管理员 - 项目内容编辑
INSERT INTO `admin_role_permissions` VALUES (1, 100, NOW(), NOW()); -- 管理员 - 项目内容删除
-- 普通用户权限
INSERT INTO `admin_role_permissions` VALUES (2, 31, NOW(), NOW()); -- 普通用户 - 系统权限控制台
INSERT INTO `admin_role_permissions` VALUES (2, 32, NOW(), NOW()); -- 普通用户 - 项目查看
INSERT INTO `admin_role_permissions` VALUES (2, 33, NOW(), NOW()); -- 普通用户 - 项目创建
INSERT INTO `admin_role_permissions` VALUES (2, 34, NOW(), NOW()); -- 普通用户 - 项目编辑 (自己的项目)
INSERT INTO `admin_role_permissions` VALUES (2, 35, NOW(), NOW()); -- 普通用户 - 项目删除 (自己的项目)
INSERT INTO `admin_role_permissions` VALUES (2, 97, NOW(), NOW()); -- 普通用户 - 项目内容查看
INSERT INTO `admin_role_permissions` VALUES (2, 98, NOW(), NOW()); -- 普通用户 - 项目内容创建
INSERT INTO `admin_role_permissions` VALUES (2, 99, NOW(), NOW()); -- 普通用户 - 项目内容编辑
INSERT INTO `admin_role_permissions` VALUES (2, 100, NOW(), NOW()); -- 普通用户 - 项目内容删除
-- 收费会员权限
INSERT INTO `admin_role_permissions` VALUES (3, 31, NOW(), NOW()); -- 收费会员 - 系统权限控制台
INSERT INTO `admin_role_permissions` VALUES (3, 32, NOW(), NOW()); -- 收费会员 - 项目查看
INSERT INTO `admin_role_permissions` VALUES (3, 33, NOW(), NOW()); -- 收费会员 - 项目创建
INSERT INTO `admin_role_permissions` VALUES (3, 34, NOW(), NOW()); -- 收费会员 - 项目编辑
INSERT INTO `admin_role_permissions` VALUES (3, 35, NOW(), NOW()); -- 收费会员 - 项目删除
INSERT INTO `admin_role_permissions` VALUES (3, 36, NOW(), NOW()); -- 收费会员 - 模块查看
INSERT INTO `admin_role_permissions` VALUES (3, 58, NOW(), NOW()); -- 收费会员 - 公告查看
INSERT INTO `admin_role_permissions` VALUES (3, 92, NOW(), NOW()); -- 收费会员 - 模块管理
INSERT INTO `admin_role_permissions` VALUES (3, 93, NOW(), NOW()); -- 收费会员 - 模块内容查看
INSERT INTO `admin_role_permissions` VALUES (3, 97, NOW(), NOW()); -- 收费会员 - 项目内容查看
INSERT INTO `admin_role_permissions` VALUES (3, 98, NOW(), NOW()); -- 收费会员 - 项目内容创建
INSERT INTO `admin_role_permissions` VALUES (3, 99, NOW(), NOW()); -- 收费会员 - 项目内容编辑
INSERT INTO `admin_role_permissions` VALUES (3, 100, NOW(), NOW()); -- 收费会员 - 项目内容删除

-- 控制面板权限分配
-- 管理员拥有所有控制面板权限
INSERT INTO `admin_role_permissions` VALUES (1, 101, NOW(), NOW()); -- 管理员 - 控制面板查看
INSERT INTO `admin_role_permissions` VALUES (1, 102, NOW(), NOW()); -- 管理员 - 控制面板日志查看

-- 收费会员可以查看控制面板，但不能查看日志
INSERT INTO `admin_role_permissions` VALUES (3, 101, NOW(), NOW()); -- 收费会员 - 控制面板查看
SET FOREIGN_KEY_CHECKS = 1;
