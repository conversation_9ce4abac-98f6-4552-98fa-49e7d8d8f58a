package utils

import (
	"go-fiber-api/database"
	"go-fiber-api/models"
	"strconv"
	"sync"
	"time"
)

var (
	settingsCache      = make(map[string]string)
	settingsCacheMutex sync.RWMutex
	lastCacheRefresh   time.Time
	cacheExpiration    = 5 * time.Minute // 缓存过期时间
)

// GetSetting 获取设置项的值
func GetSetting(key string, defaultValue string) string {
	// 检查缓存是否过期
	settingsCacheMutex.RLock()
	if time.Since(lastCacheRefresh) > cacheExpiration {
		settingsCacheMutex.RUnlock()
		RefreshSettingsCache()
	} else {
		defer settingsCacheMutex.RUnlock()
		if value, exists := settingsCache[key]; exists {
			return value
		}
	}

	// 如果缓存中没有，从数据库获取
	db := database.GetDB()
	var setting models.Settings
	result := db.Where("`key` = ?", key).First(&setting)
	if result.Error != nil {
		return defaultValue
	}

	// 更新缓存
	settingsCacheMutex.Lock()	
	defer settingsCacheMutex.Unlock()
	settingsCache[key] = setting.Value
	return setting.Value
}

// GetSettingBool 获取布尔类型的设置项
func GetSettingBool(key string, defaultValue bool) bool {
	value := GetSetting(key, strconv.FormatBool(defaultValue))
	boolValue, err := strconv.ParseBool(value)
	if err != nil {
		return defaultValue
	}
	return boolValue
}

// GetSettingInt 获取整数类型的设置项
func GetSettingInt(key string, defaultValue int) int {
	value := GetSetting(key, strconv.Itoa(defaultValue))
	intValue, err := strconv.Atoi(value)
	if err != nil {
		return defaultValue
	}
	return intValue
}

// RefreshSettingsCache 刷新设置缓存
func RefreshSettingsCache() {
	db := database.GetDB()
	var settings []models.Settings
	result := db.Find(&settings)
	if result.Error != nil {
		return
	}

	// 更新缓存
	newCache := make(map[string]string)
	for _, setting := range settings {
		newCache[setting.Key] = setting.Value
	}

	settingsCacheMutex.Lock()
	defer settingsCacheMutex.Unlock()
	settingsCache = newCache
	lastCacheRefresh = time.Now()
}

// IsInvitationRequired 检查是否需要邀请码
func IsInvitationRequired() bool {
	return GetSettingBool("invitation_required", true)
}

// GetInvitationCodeLength 获取邀请码长度
func GetInvitationCodeLength() int {
	return GetSettingInt("invitation_code_length", 8)
}

// GetInvitationCodeExpireDays 获取邀请码过期天数
func GetInvitationCodeExpireDays() int {
	return GetSettingInt("invitation_code_expire_days", 30)
}

// GetInvitationCodeMaxUses 获取邀请码最大使用次数
func GetInvitationCodeMaxUses() int {
	// 获取设置值，默认为0（表示不限制）
	maxUses := GetSettingInt("invitation_code_max_uses", 0)
	return maxUses
}

// IsEmailVerificationRequired 检查是否需要邮箱验证
func IsEmailVerificationRequired() bool {
	return GetSettingBool("email_verification_required", false)
}

// GetEmailVerificationExpireMinutes 获取邮箱验证过期分钟数
func GetEmailVerificationExpireMinutes() int {
	return GetSettingInt("email_verification_expire_minutes", 30)
}

// GetPasswordResetExpireMinutes 获取密码重置过期分钟数
func GetPasswordResetExpireMinutes() int {
	return GetSettingInt("password_reset_expire_minutes", 60)
}

// IsSiteClosed 检查网站是否处于维护模式
func IsSiteClosed() bool {
	return GetSettingBool("site_closed", false)
} 