package utils

import (
	"encoding/json"
	"fmt"
	"go-fiber-api/config"
	"go-fiber-api/database"
	"io"
	"log"
	"net/http"
	"net/url"
	"strings"
	"time"
)

// RecaptchaResponse Google reCAPTCHA API 响应结构体
type RecaptchaResponse struct {
	Success     bool     `json:"success"`
	ChallengeTS string   `json:"challenge_ts"`
	Hostname    string   `json:"hostname"`
	ErrorCodes  []string `json:"error-codes"`
}

// RecaptchaVerificationRequest reCAPTCHA 验证请求结构体
type RecaptchaVerificationRequest struct {
	Token      string `json:"token" validate:"required"`      // reCAPTCHA 响应 token
	RemoteIP   string `json:"remote_ip,omitempty"`            // 用户 IP 地址（可选）
}

const (
	// Google reCAPTCHA v2 验证 API 端点
	RecaptchaVerifyURL = "https://www.google.com/recaptcha/api/siteverify"

	// 验证超时时间
	RecaptchaTimeout = 30 * time.Second

	// 重试次数
	RecaptchaMaxRetries = 3
)

// IsRecaptchaEnabled 检查 reCAPTCHA 是否启用
func IsRecaptchaEnabled() bool {
	cfg := config.LoadConfig()
	return cfg.RecaptchaConfig.Enabled && 
		   cfg.RecaptchaConfig.SecretKey != "" && 
		   cfg.RecaptchaConfig.SiteKey != ""
}

// GetRecaptchaSiteKey 获取 reCAPTCHA 站点密钥
func GetRecaptchaSiteKey() string {
	cfg := config.LoadConfig()
	return cfg.RecaptchaConfig.SiteKey
}

// VerifyRecaptcha 验证 Google reCAPTCHA v2 token
// 参数:
//   - token: 前端 reCAPTCHA 组件返回的响应 token
//   - remoteIP: 用户的 IP 地址（可选，用于增强安全性）
// 返回:
//   - bool: 验证是否成功
//   - error: 错误信息
func VerifyRecaptcha(token, remoteIP string) (bool, error) {
	cfg := config.LoadConfig()

	// 检查 reCAPTCHA 是否启用
	if !IsRecaptchaEnabled() {
		log.Println("[reCAPTCHA] reCAPTCHA 验证未启用，跳过验证")
		return true, nil // 如果未启用，则跳过验证
	}

	// 开发环境下，如果使用测试密钥，可以选择跳过实际验证
	if cfg.AppEnv == "development" && cfg.RecaptchaConfig.SiteKey == "6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI" {
		log.Println("[reCAPTCHA] 开发环境使用测试密钥，跳过实际验证")
		return true, nil
	}

	// 验证输入参数
	if token == "" {
		return false, fmt.Errorf("reCAPTCHA token 不能为空")
	}
	
	// 构建请求参数
	data := url.Values{}
	data.Set("secret", cfg.RecaptchaConfig.SecretKey)
	data.Set("response", token)
	if remoteIP != "" {
		data.Set("remoteip", remoteIP)
	}

	// 创建 HTTP 客户端
	client := &http.Client{
		Timeout: RecaptchaTimeout,
	}

	// 重试机制
	var resp *http.Response
	var err error
	for attempt := 1; attempt <= RecaptchaMaxRetries; attempt++ {
		log.Printf("[reCAPTCHA] 尝试验证 (第 %d/%d 次)", attempt, RecaptchaMaxRetries)

		// 发送 POST 请求到 Google reCAPTCHA API
		resp, err = client.PostForm(RecaptchaVerifyURL, data)
		if err == nil {
			break // 成功，跳出重试循环
		}

		log.Printf("[reCAPTCHA] 第 %d 次请求失败: %v", attempt, err)

		// 如果不是最后一次尝试，等待一段时间再重试
		if attempt < RecaptchaMaxRetries {
			waitTime := time.Duration(attempt) * time.Second
			log.Printf("[reCAPTCHA] 等待 %v 后重试...", waitTime)
			time.Sleep(waitTime)
		}
	}

	if err != nil {
		log.Printf("[reCAPTCHA] 所有重试都失败了: %v", err)
		return false, fmt.Errorf("reCAPTCHA 验证请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("[reCAPTCHA] 读取响应失败: %v", err)
		return false, fmt.Errorf("读取 reCAPTCHA 响应失败: %v", err)
	}

	// 解析 JSON 响应
	var recaptchaResp RecaptchaResponse
	if err := json.Unmarshal(body, &recaptchaResp); err != nil {
		log.Printf("[reCAPTCHA] 解析响应 JSON 失败: %v", err)
		return false, fmt.Errorf("解析 reCAPTCHA 响应失败: %v", err)
	}

	// 记录验证结果
	if cfg.AppEnv == "development" {
		log.Printf("[reCAPTCHA] 验证结果: Success=%v, ErrorCodes=%v", 
			recaptchaResp.Success, recaptchaResp.ErrorCodes)
	}

	// 检查验证结果
	if !recaptchaResp.Success {
		errorMsg := "reCAPTCHA 验证失败"
		if len(recaptchaResp.ErrorCodes) > 0 {
			errorMsg = fmt.Sprintf("reCAPTCHA 验证失败: %s", 
				strings.Join(recaptchaResp.ErrorCodes, ", "))
		}
		log.Printf("[reCAPTCHA] %s", errorMsg)
		return false, fmt.Errorf(errorMsg)
	}

	log.Printf("[reCAPTCHA] 验证成功")
	return true, nil
}

// ValidateRecaptchaToken 验证 reCAPTCHA token 的格式
func ValidateRecaptchaToken(token string) error {
	if token == "" {
		return fmt.Errorf("reCAPTCHA token 不能为空")
	}
	
	// reCAPTCHA token 通常是一个长字符串，包含字母、数字、下划线和连字符
	// 这里做基本的长度和字符检查
	if len(token) < 20 {
		return fmt.Errorf("reCAPTCHA token 格式无效")
	}
	
	return nil
}

// GetRecaptchaErrorMessage 根据错误代码返回用户友好的错误消息
func GetRecaptchaErrorMessage(errorCodes []string) string {
	if len(errorCodes) == 0 {
		return "reCAPTCHA 验证失败"
	}

	// 错误代码映射
	errorMessages := map[string]string{
		"missing-input-secret":   "reCAPTCHA 配置错误：缺少密钥",
		"invalid-input-secret":   "reCAPTCHA 配置错误：密钥无效",
		"missing-input-response": "请完成 reCAPTCHA 验证",
		"invalid-input-response": "reCAPTCHA 验证无效，请重试",
		"bad-request":            "reCAPTCHA 请求格式错误",
		"timeout-or-duplicate":   "reCAPTCHA 已过期或重复使用，请重新验证",
	}

	// 返回第一个已知错误的消息，如果都是未知错误则返回通用消息
	for _, code := range errorCodes {
		if msg, exists := errorMessages[code]; exists {
			return msg
		}
	}

	return fmt.Sprintf("reCAPTCHA 验证失败: %s", strings.Join(errorCodes, ", "))
}

// --- 临时数据存储 (用于 2FA 设置等) ---

// StoreTemporaryData 存储临时数据到 Redis
func StoreTemporaryData(key, value string, expiration time.Duration) error {
	return database.Rdb.Set(database.RedisCtx, key, value, expiration).Err()
}

// RetrieveTemporaryData 从 Redis 获取临时数据
func RetrieveTemporaryData(key string) (string, bool) {
	val, err := database.Rdb.Get(database.RedisCtx, key).Result()
	if err != nil {
		return "", false
	}
	return val, true
}

// ClearTemporaryData 清除 Redis 中的临时数据
func ClearTemporaryData(key string) error {
	return database.Rdb.Del(database.RedisCtx, key).Err()
}
