package utils

import (
	"bytes"
	"errors"
	"fmt"
	"image"
	"image/color"
	"image/png"
	"io/fs"
	"log"
	"math/rand"
	"os"
	"path/filepath"
	"strings"
	"time"

	"go-fiber-api/database"

	"github.com/fogleman/gg"
	"github.com/go-redis/redis/v8"
	xdraw "golang.org/x/image/draw" // Import the extended draw package
	"golang.org/x/image/font"
	"golang.org/x/image/font/basicfont"
	// If using TTF fonts:
	// "github.com/golang/freetype/truetype"
	// "golang.org/x/image/font"
)

const (
	defaultLength      = 5
	defaultWidth       = 150 // Increased default width
	defaultHeight      = 50  // Increased default height
	defaultQuality     = 90  // Not directly used for PNG, relevant for JPEG
	defaultMaxAngle    = 15
	defaultNumLines    = 3
	defaultTextPadding = 10              // Padding from left/right edges
	captchaPrefix      = "captcha:"      // Redis key prefix
	captchaExpiration  = 5 * time.Minute // Captchas expire after 5 minutes
)

// Config holds captcha generation parameters.
type Config struct {
	Length      int      // Number of characters in the captcha.
	Width       int      // Width of the captcha image.
	Height      int      // Height of the captcha image.
	FontColors  []string // Optional: List of hex colors for fonts (e.g., "#ff0000"). If empty, random colors are used.
	MaxAngle    int      // Maximum rotation angle for characters.
	NumLines    int      // Number of distraction lines to draw.
	CharSet     string   // Characters to use for generation.
	Sensitive   bool     // Whether the captcha is case-sensitive.
	Math        bool     // Use math challenge instead of text.
	Fonts       []string // List of paths to TTF font files. If empty, uses a basic built-in font.
	Backgrounds []string // List of paths to background image files. If empty, uses solid color.
	BgColor     string   // Background color hex (e.g., "#ffffff"). If empty, defaults to white.
	UseBgImage  bool     // Whether to use background images.
	// Effects (Optional - would require another library like disintegration/imaging)
	// Contrast     float64
	// Sharpen      float64
	// Blur         float64
	// Invert       bool
}

// Result holds the generated captcha data.
type Result struct {
	Answer string // The correct answer (string or math result).
	Image  []byte // PNG encoded image bytes.
	Err    error  // Any error during generation.
}

// Global random source seeded once
var seededRand *rand.Rand = rand.New(
	rand.NewSource(time.Now().UnixNano()))

// Generate creates a new captcha image based on the config.
func Generate(cfg Config) Result {
	// Apply defaults
	if cfg.Length <= 0 {
		cfg.Length = defaultLength
	}
	if cfg.Width <= 0 {
		cfg.Width = defaultWidth
	}
	if cfg.Height <= 0 {
		cfg.Height = defaultHeight
	}
	if cfg.CharSet == "" {
		// Default charset excluding visually similar chars (0, O, 1, I, l)
		cfg.CharSet = "23456789abcdefghijkmnpqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ"
	}
	if cfg.MaxAngle <= 0 {
		cfg.MaxAngle = defaultMaxAngle
	}
	if cfg.NumLines < 0 {
		cfg.NumLines = defaultNumLines
	}
	if cfg.BgColor == "" {
		cfg.BgColor = "#ffffff"
	}

	var answer string
	var textToDraw string

	// 1. Generate Text or Math Problem
	if cfg.Math {
		x := seededRand.Intn(21) + 10 // 10-30
		y := seededRand.Intn(9) + 1   // 1-9
		textToDraw = fmt.Sprintf("%d + %d = ?", x, y)
		answer = fmt.Sprintf("%d", x+y)
	} else {
		chars := []rune(cfg.CharSet)
		textRunes := make([]rune, cfg.Length)
		for i := 0; i < cfg.Length; i++ {
			textRunes[i] = chars[seededRand.Intn(len(chars))]
		}
		textToDraw = string(textRunes)
		if cfg.Sensitive {
			answer = textToDraw
		} else {
			answer = strings.ToLower(textToDraw) // Store lowercase answer if not sensitive
		}
	}

	// 2. Create Image Canvas
	dc := gg.NewContext(cfg.Width, cfg.Height)

	// 3. Set Background
	if cfg.UseBgImage && len(cfg.Backgrounds) > 0 {
		bgPath := cfg.Backgrounds[seededRand.Intn(len(cfg.Backgrounds))]
		bgImage, err := gg.LoadImage(bgPath)
		if err != nil {
			log.Printf("Captcha Warning: Failed to load background %s: %v. Using solid color.", bgPath, err)
			setColor(dc, cfg.BgColor)
			dc.Clear()
		} else {
			scaledBg := resizeImage(bgImage, cfg.Width, cfg.Height) // Use the corrected resizeImage
			dc.DrawImage(scaledBg, 0, 0)
		}
	} else {
		setColor(dc, cfg.BgColor)
		dc.Clear()
	}

	// 4. Load Fonts
	// Declare fontFace as the interface type font.Face
	var fontFace font.Face = basicfont.Face7x13 // Basic fallback font
	fontSize := float64(cfg.Height) * 0.6

	if len(cfg.Fonts) > 0 {
		fontPath := cfg.Fonts[seededRand.Intn(len(cfg.Fonts))]
		loadedFace, err := gg.LoadFontFace(fontPath, fontSize)
		if err != nil {
			log.Printf("Captcha Warning: Failed to load font %s: %v. Using basic font.", fontPath, err)
			// Keep the basicfont as fontFace
		} else {
			fontFace = loadedFace // Assign the loaded interface value
		}
	}
	dc.SetFontFace(fontFace)

	// 5. Draw Text
	charWidth := float64(cfg.Width-2*defaultTextPadding) / float64(len(textToDraw))
	for i, char := range textToDraw {
		dc.Push() // Save context state

		// Random color
		if len(cfg.FontColors) > 0 {
			setColor(dc, cfg.FontColors[seededRand.Intn(len(cfg.FontColors))])
		} else {
			dc.SetColor(randomDarkColor()) // Use dark colors for better contrast usually
		}

		// Random angle
		angle := float64(seededRand.Intn(2*cfg.MaxAngle+1) - cfg.MaxAngle) // -MaxAngle to +MaxAngle
		radAngle := gg.Radians(angle)

		// Calculate position
		x := float64(defaultTextPadding) + charWidth*float64(i) + charWidth/2
		y := float64(cfg.Height) / 2 // Center vertically (adjust as needed)

		// Apply rotation and draw
		dc.RotateAbout(radAngle, x, y)
		// Adjust font size slightly randomly per character? Might make it too hard.
		// dc.SetFontFace(adjustFontSize(fontFace, fontSize))
		dc.DrawStringAnchored(string(char), x, y, 0.5, 0.5) // Anchor at center

		dc.Pop() // Restore context state
	}

	// 6. Draw Distraction Lines
	for i := 0; i < cfg.NumLines; i++ {
		x1 := seededRand.Float64() * float64(cfg.Width)
		y1 := seededRand.Float64() * float64(cfg.Height)
		x2 := seededRand.Float64() * float64(cfg.Width)
		y2 := seededRand.Float64() * float64(cfg.Height)

		// Random color for lines
		if len(cfg.FontColors) > 0 {
			setColor(dc, cfg.FontColors[seededRand.Intn(len(cfg.FontColors))])
		} else {
			dc.SetColor(randomDarkColor())
		}
		dc.SetLineWidth(1) // Adjust line width if needed
		dc.DrawLine(x1, y1, x2, y2)
		dc.Stroke()
	}

	// 7. Apply Post-processing Effects (Requires imaging library - Example)
	// finalImage := dc.Image()
	// if cfg.Blur > 0 { finalImage = imaging.Blur(finalImage, cfg.Blur) }
	// ... other effects ...
	// dc = gg.NewContextForImage(finalImage) // If effects were applied

	// 8. Encode Image
	buffer := new(bytes.Buffer)
	err := png.Encode(buffer, dc.Image())
	if err != nil {
		return Result{Err: fmt.Errorf("failed to encode PNG: %w", err)}
	}

	return Result{ // Ensure this part is correct
		Answer: answer,
		Image:  buffer.Bytes(),
		Err:    nil,
	}
}

// Helper to set color from hex string
func setColor(dc *gg.Context, hexColor string) {
	r, g, b, a, err := parseHexColor(hexColor)
	if err != nil {
		log.Printf("Captcha Warning: Invalid hex color '%s': %v. Using black.", hexColor, err)
		dc.SetRGB(0, 0, 0) // Default to black on error
		return
	}
	dc.SetRGBA255(r, g, b, a)
}

// Helper to parse hex color string (e.g., "#RRGGBB" or "#RGB")
func parseHexColor(s string) (r, g, b, a int, err error) {
	s = strings.TrimPrefix(s, "#")
	a = 255 // Default alpha
	var format string
	switch len(s) {
	case 6:
		format = "%02x%02x%02x"
	case 3:
		format = "%1x%1x%1x"
		s = string([]byte{s[0], s[0], s[1], s[1], s[2], s[2]}) // Expand #RGB to #RRGGBB
	default:
		err = errors.New("invalid hex color format")
		return
	}
	_, err = fmt.Sscanf(s, format, &r, &g, &b)
	return
}

// Helper for random dark colors (better contrast on light backgrounds)
func randomDarkColor() color.Color {
	r := uint8(seededRand.Intn(128)) // 0-127
	g := uint8(seededRand.Intn(128))
	b := uint8(seededRand.Intn(128))
	return color.RGBA{R: r, G: g, B: b, A: 255}
}

// Helper to resize image (simple implementation)
func resizeImage(img image.Image, width, height int) image.Image {
	newImg := image.NewRGBA(image.Rect(0, 0, width, height))
	// Use the imported xdraw package's NearestNeighbor
	xdraw.NearestNeighbor.Scale(newImg, newImg.Rect, img, img.Bounds(), xdraw.Src, nil)
	return newImg
}

// --- Utility Functions for Loading Assets (Example) ---

// LoadFilesFromDir scans a directory for files with given extensions.
func LoadFilesFromDir(dir string, extensions []string) ([]string, error) {
	var files []string
	err := filepath.WalkDir(dir, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err // Propagate errors (e.g., permission denied)
		}
		if !d.IsDir() {
			ext := strings.ToLower(filepath.Ext(path))
			for _, allowedExt := range extensions {
				if ext == allowedExt {
					files = append(files, path)
					break
				}
			}
		}
		return nil
	})
	if err != nil {
		// Handle cases where the directory might not exist gracefully
		if os.IsNotExist(err) {
			log.Printf("Captcha Info: Asset directory not found: %s", dir)
			return []string{}, nil // Return empty list, not an error
		}
		return nil, fmt.Errorf("error walking directory %s: %w", dir, err)
	}
	return files, nil
}

var buffer *bytes.Buffer

// --- Redis Captcha Storage ---

// StoreCaptcha stores the captcha answer in Redis with an expiration.
// The answer is stored in lowercase for case-insensitive comparison.
func StoreCaptcha(key, answer string) error {
	return database.Rdb.Set(database.RedisCtx, captchaPrefix+key, strings.ToLower(answer), captchaExpiration).Err()
}

// RetrieveAndClearCaptcha retrieves the captcha answer from Redis and deletes the key.
// Returns the answer (lowercase) and true if found, otherwise empty string and false.
func RetrieveAndClearCaptcha(key string) (string, bool) {
	redisKey := captchaPrefix + key
	answer, err := database.Rdb.Get(database.RedisCtx, redisKey).Result()

	if err == redis.Nil {
		return "", false // Key not found
	} else if err != nil {
		log.Printf("Redis GET error for captcha key '%s': %v", key, err)
		return "", false // Error retrieving
	}

	// Delete the key after successful retrieval
	if delErr := database.Rdb.Del(database.RedisCtx, redisKey).Err(); delErr != nil {
		log.Printf("Redis DEL error for captcha key '%s': %v", key, delErr)
		// Log the error but continue, as we already got the value
	}

	return answer, true
}

// VerifyCaptcha checks the user's input against the stored answer in Redis.
// It performs a case-insensitive comparison and clears the captcha key upon successful retrieval.
func VerifyCaptcha(key, value string) bool {
	storedAnswer, found := RetrieveAndClearCaptcha(key)
	if !found {
		log.Printf("Captcha Verify Failed: Key '%s' not found or expired.", key)
		return false
	}
	// Case-insensitive comparison
	match := strings.ToLower(value) == storedAnswer
	if !match {
		log.Printf("Captcha Verify Failed: Key '%s', Input '%s', Expected '%s'", key, value, storedAnswer)
	}
	return match
}

// --- End Redis Captcha Storage ---
