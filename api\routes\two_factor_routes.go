package routes

import (
	"go-fiber-api/api/handlers"
	"go-fiber-api/api/middleware"

	"github.com/gofiber/fiber/v2"
)

// SetupTwoFactorRoutes 设置双因素认证相关路由
func SetupTwoFactorRoutes(router fiber.Router) {
	// 2FA 路由组，需要认证 - 使用不同的路径避免与公开认证路由冲突
	twoFactor := router.Group("/2fa", middleware.AuthMiddleware())

	// 获取 2FA 状态
	twoFactor.Get("/status", handlers.GetTwoFactorStatus)

	// 生成 2FA 密钥和二维码
	twoFactor.Post("/generate", handlers.GenerateTwoFactorSecret)

	// 验证并启用 2FA
	twoFactor.Post("/verify", handlers.VerifyTwoFactorSetup)

	// 禁用 2FA
	twoFactor.Post("/disable", handlers.DisableTwoFactor)
}
