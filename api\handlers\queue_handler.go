package handlers

import (
	"go-fiber-api/utils/queue"
	"github.com/gofiber/fiber/v2"
)

// GetQueueStats 获取队列统计信息
// @Summary      获取队列统计信息
// @Description  获取当前所有队列的统计数据（如任务数、延迟等）
// @Tags         Queue
// @Accept       json
// @Produce      json
// @Success      200 {object} dto.StandardResponse{data=object} "获取成功"
// @Failure      500 {object} dto.StandardResponse "服务器内部错误"
// @Router       /queue/stats [get]
func GetQueueStats(c *fiber.Ctx) error {
	stats, err := queue.GetQueueStats()
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "获取队列统计信息失败: " + err.Error(),
		})
	}

	return c.JSON(fiber.Map{
		"status": "success",
		"data":   stats,
	})
} 