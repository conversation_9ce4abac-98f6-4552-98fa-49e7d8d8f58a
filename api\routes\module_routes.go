package routes

import (
	"go-fiber-api/api/handlers"
	"go-fiber-api/api/middleware"
	"go-fiber-api/utils"

	"github.com/gofiber/fiber/v2"
)

// SetupModuleRoutes 设置模块管理相关路由
func SetupModuleRoutes(router fiber.Router) {
	// 模块路由组
	modules := router.Group("/module")
	
	// 添加认证中间件
	modules.Use(utils.RequireAuthentication)

	// 模块CRUD路由
	modules.Get("/index", middleware.RequirePermission("module.view"), handlers.GetModules)           // 获取模块列表
	modules.Get("/index/:id", middleware.RequirePermission("module.view"), handlers.GetModule)        // 获取单个模块
	modules.Post("/index", middleware.RequirePermission("module.create"), handlers.CreateModule)        // 创建模块
	modules.Put("/index/:id", middleware.RequirePermission("module.edit"), handlers.UpdateModule)     // 更新模块
	modules.Delete("/index/:id", middleware.RequirePermission("module.delete"), handlers.DeleteModule)  // 删除模块
	// 添加恢复模块路由
	modules.Put("/restore/:id", middleware.RequirePermission("module.edit"), handlers.RestoreModule)
} 